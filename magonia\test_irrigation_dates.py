import os
import random
import string
import time
import traceback
from flask import json
import requests
import json
from dotenv import load_dotenv
load_dotenv()

token = os.getenv('AUTH_TOKEN')


def send_prompt(prompt, session_id="session123", user_id="f68381cd-a748-47bd-842c-701790b35e3c", token=None):
    url = "http://127.0.0.1:8080/magonia/chat"
    data = {
        "session_id": session_id,
        "prompt": prompt,
        "chat_history": "",
        "scopes": ["magonia-api"],
        "user_id": user_id
    }
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {token}"
    }
    response = requests.post(url, headers=headers, data=json.dumps(data))
    print("Status Code:", response.status_code)

    if response.status_code != 200:
        print("Response Text:", response.text)
        try:
            return response.text
        except Exception as e:
            print(e)
            traceback.print_exc()
            return {
                'error': 'An unexpected error occurred, and the response is not valid JSON.',
                'status_code': response.status_code
            }

    try:
        json_response = response.json()
        print("\033[33m" + "Response: " + json_response + "\033[0m")
        return json_response
    except Exception as e:
        print(e)
        traceback.print_exc()
        return None


# Prompts to test different dates for the same field
prompts = [
    # Test different dates for the same field to see if the API returns different values
    "Do I have to irrigate my field CHlewi on 2024-01-28",
    "Do I have to irrigate my field CHlewi on 2024-02-15",
    "Do I have to irrigate my field CHlewi on 2024-03-10",
    "Do I have to irrigate my field CHlewi on 2024-04-05",
    "Do I have to irrigate my field CHlewi on 2024-05-20",
    
    # Test a different field
    "Do I have to irrigate my field Kalel on 2024-01-28",
    
    # Get field list for reference
    "quelles sont mes parcelles ?"
]

# Start the timer
start_time = time.time()
session_id = "test_session_" + ''.join(random.choices(string.ascii_lowercase + string.digits, k=8))
user_id = "f68381cd-a748-47bd-842c-701790b35e3c"
print(f"Using session_id: {session_id}")
print(f"Using user_id: {user_id}")

# Your prompt processing loop
for prompt in prompts:
    start_time = time.time()
    print("\033[31m" + "PROMPT : " + "\033[0m" + "\033[32m" + prompt + "\033[0m")
    send_prompt(prompt=prompt, session_id=session_id, user_id=user_id, token=token)
    end_time = time.time()
    execution_time = end_time - start_time
    print(f"Execution time: {execution_time:.2f} seconds")
    print("-" * 80)  # Add a separator between prompts
    time.sleep(1)  # Add a small delay between requests

# End the timer
end_time = time.time()
execution_time = end_time - start_time

if execution_time < 1:
    execution_message = f"{execution_time * 1000:.2f} ms"
elif execution_time < 60:
    execution_message = f"{execution_time:.2f} seconds"
else:
    minutes = execution_time // 60
    seconds = execution_time % 60
    execution_message = f"{int(minutes)} minute(s) and {seconds:.2f} seconds"

print(f"Total execution time: {execution_message}")
