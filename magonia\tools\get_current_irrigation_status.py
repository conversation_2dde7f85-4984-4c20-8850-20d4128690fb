from typing import Optional
from flask import g
from langchain_core.tools import tool

@tool
def get_current_irrigation_status(tool_input: Optional[str] = None) -> str:
    """
    Retrieve the current irrigation status for all fields, showing which fields are actively being irrigated.

    USE THIS TOOL WHEN:
    - The user asks which fields are currently being irrigated
    - The user wants to know the current irrigation status of their fields
    - The user asks about ongoing irrigation activities
    - The user wants to check if irrigation systems are active

    DO NOT USE THIS TOOL WHEN:
    - The user asks about future irrigation needs (use check_irrigation_need_for_x_days)
    - The user asks about irrigation recommendations (use check_today_active_irrigation_user)
    - The user asks about a specific field's irrigation history
    - The user is asking about soil moisture, not active irrigation

    EXAMPLE QUERIES:
    - "Which fields are being irrigated right now?"
    - "What's the current irrigation status of my fields?"
    - "Are any of my irrigation systems running?"
    - "Show me the active irrigation status"

    Args:
        tool_input (str, optional): Ignored parameter, exists for compatibility.

    Returns:
        str: A detailed summary of which fields are currently being irrigated, including
             start times and irrigation volumes where available.
    """


    try:

        response = g.seabex_api.tools().irrigations().call_tool(
            'get_current_irrigation_status'
        )

        if isinstance(response, dict) and 'error' in response:
            error_message = response['error']
            return f"Error: {error_message}"

        # Process the response to make it more user-friendly
        if isinstance(response, dict):
            fields_data = response.get('fields', [])

            # Filter fields that are currently being irrigated
            active_fields = [
                field for field in fields_data
                if field.get('is_irrigating', False)
            ]

            if active_fields:
                # Format the response
                result = "Fields currently being irrigated:\n\n"

                for field in active_fields:
                    field_name = field.get('field_name', 'Unknown field')
                    start_time = field.get('irrigation_start_time', 'Unknown time')
                    volume = field.get('irrigation_volume', 'Unknown volume')

                    result += (
                        f"• {field_name}\n"
                        f"  - Started at: {start_time}\n"
                        f"  - Volume: {volume} mm\n\n"
                    )

                # Add summary
                result += f"In total, {len(active_fields)} field(s) are currently being irrigated."
                return result
            else:
                return "None of your fields are currently being irrigated."

        # If response is a string or other format, return it directly
        return f"Current irrigation status: {response}"

    except Exception as e:
        print(f"Error retrieving irrigation data: {e}")
        return (
            "I'm sorry, I couldn't retrieve the irrigation information at the moment. "
            "Please try again later."
        )
