import os


class Config:



    def __init__(self):
        pass

    DEBUG = os.getenv('DEBUG', False)
    PORT = os.getenv('PORT', '8888')
    IMAGE_TAG = os.getenv('IMAGE_TAG', 'v0.0.1')
    GCP_PROJECT_ID = os.getenv('GCP_PROJECT_ID', 'seabex-saas')
    MODEL_REGION = os.getenv('MODEL_REGION', 'us-central1')
    MODEL_NAME = os.getenv('MODEL_NAME', 'mistral-large')
    MODEL_VERSION = os.getenv('MODEL_VERSION', '2407')
    GOOGLE_APPLICATION_CREDENTIALS = os.getenv('GOOGLE_APPLICATION_CREDENTIALS', 'creds.json')
    GPU = os.getenv('GPU', 'TRUE')
    AUTH_TOKEN = os.getenv('AUTH_TOKEN', '')
    ALLOWED_ORIGINS = os.getenv('ALLOWED_ORIGINS', '').split(',')