import json
from typing import Dict, List, Optional

# In-memory storage for when <PERSON><PERSON> is in read-only mode
memory_store: Dict[str, List[str]] = {}

def store_memory(user_id: str, memory: str) -> bool:
    """
    Store a memory for a user in the in-memory store.
    
    Args:
        user_id: The user ID to store the memory for
        memory: The memory text to store
        
    Returns:
        bool: True if the memory was stored, False otherwise
    """
    if not user_id or not memory:
        return False
        
    if user_id not in memory_store:
        memory_store[user_id] = []
        
    if memory not in memory_store[user_id]:
        memory_store[user_id].append(memory)
        return True
        
    return False
    
def get_memories(user_id: str) -> List[str]:
    """
    Get all memories for a user from the in-memory store.
    
    Args:
        user_id: The user ID to get memories for
        
    Returns:
        List[str]: List of memories for the user
    """
    if not user_id or user_id not in memory_store:
        return []
        
    return memory_store[user_id]
    
def delete_memory(user_id: str, memory: Optional[str] = None) -> bool:
    """
    Delete a memory or all memories for a user from the in-memory store.
    
    Args:
        user_id: The user ID to delete memories for
        memory: The specific memory to delete, or None to delete all
        
    Returns:
        bool: True if the memory/memories were deleted, False otherwise
    """
    if not user_id or user_id not in memory_store:
        return False
        
    if memory:
        if memory in memory_store[user_id]:
            memory_store[user_id].remove(memory)
            return True
        return False
    else:
        memory_store.pop(user_id)
        return True
