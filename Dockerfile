# Use official Python image as base
FROM python:3.10-slim

# Build-time arguments
ARG PORT=80

# Set environment variables
ENV PORT=${PORT}
ENV DEBIAN_FRONTEND=noninteractive

# Set working directory
WORKDIR /app

# Copy only necessary files first for caching layers
COPY requirements.txt ./

# Install dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Copy the rest of the application code
COPY . .

# Set entrypoint to run the application
ENTRYPOINT ["python", "-m", "magonia.main"]
