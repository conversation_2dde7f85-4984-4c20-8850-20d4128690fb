from datetime import datetime
from typing import Optional, Union
from flask import g
from langchain_core.tools import tool

@tool
def get_lowest_soil_water_volume(calculation_date: Optional[Union[datetime, str]] = None) -> dict:
    """
    Retrieve the fields with the lowest soil water volume for today.

    Returns:
        dict: A list of fields with their soil water volume or an error message.
    """
    try:

        response = g.seabex_api.tools().soils().call_tool(
            'get_lowest_soil_water_volume'
        )
        
        if isinstance(response, dict) and 'error' in response:
            error_message = response['error']
            return {"message": f"Error: {error_message}"}

        return response

    except Exception as e:
        print(f"Error retrieving lowest soil water volume: {e}")
        return {
            "message": "I'm sorry, I couldn't retrieve the soil water volume information at the moment. Please try again later."
        }
