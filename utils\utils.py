
from datetime import datetime
import re


def extract_value(input_str, key_name):
    """
    Helper function to extract a value from a string based on a key.

    Args:
        input_str (str): The input string containing key-value pairs.
        key_name (str): The key to search for in the input string.

    Returns:
        str: The extracted value, or the original input string if no match is found.
    """
    pattern = f"{key_name}\\s*[:=]\\s*(.*)"
    match = re.match(pattern, input_str)
    data =  match.group(1).strip() if match else input_str.strip()

    return data.replace('"', '').replace("'", '')

def clean_and_format_date(input_date, field):
    """
    Cleans and formats the input date.

    Args:
        input_date (str or datetime): The date to be cleaned and formatted.

    Returns:
        str: The formatted date string in 'YYYY-MM-DD' format.
    """

    if isinstance(input_date, str):
        input_date = extract_value(input_date, field)

    if isinstance(input_date, datetime):
        return input_date.strftime('%Y-%m-%d')

    return input_date

def parse_input(input_string):
    """
    Parse input string that might be in various formats:
    - JSON-like format: {"key1": "value1", "key2": "value2"}
    - Key-value pairs separated by commas: key1=value1, key2=value2
    - Key-value pairs separated by newlines: key1=value1\nkey2=value2

    Args:
        input_string (str): The input string to parse.

    Returns:
        dict: A dictionary of parsed key-value pairs.
    """
    import json

    # If input is None or empty, return empty dict
    if not input_string:
        return {}

    # Try to parse as JSON first
    if input_string.strip().startswith('{') and input_string.strip().endswith('}'):
        try:
            # Clean up the string to handle potential formatting issues
            cleaned_input = input_string.replace("'", '"')
            return json.loads(cleaned_input)
        except json.JSONDecodeError:
            # If JSON parsing fails, continue with other methods
            pass

    # Replace newlines with commas, then split by commas
    parts = [part.strip() for part in input_string.replace('\n', ',').split(',') if part.strip()]

    # Create a dictionary to hold the parsed values
    parsed_data = {}

    for part in parts:
        # Check for '=' or ':' for key-value pairs
        if '=' in part:
            key, value = part.split('=', 1)  # Split only on the first equals sign
        elif ':' in part:
            key, value = part.split(':', 1)  # Split only on the first colon
        else:
            print(f"Warning: No key-value pair found in part: '{part}'")
            continue

        # Store key-value pairs, stripping whitespace
        parsed_data[key.strip()] = value.strip()

    return parsed_data


def parse_integer_from_string(input_string: str) -> int:
    """
    Parse an integer value from a string that may be in the format 'variable = value'
    or just a value.

    Args:
        input_string (str): The input string to parse.

    Returns:
        int: The parsed integer value.

    Raises:
        ValueError: If the input cannot be converted to an integer.
    """
    # Check if the input contains '='
    if '=' in input_string:
        # Split by '=' and extract the value
        value_str = input_string.split('=')[1].strip()
    else:
        # If it's just a number, use the input string directly
        value_str = input_string.strip()

    # Convert to integer with error handling
    try:
        return int(value_str)
    except ValueError:
        raise ValueError(f"Invalid input for conversion to integer: '{value_str}'")