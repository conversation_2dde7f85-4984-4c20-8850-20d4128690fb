import os
import sys
import time
import random
from datetime import datetime, timedelta
from dotenv import load_dotenv

# Add the parent directory to the path so we can import magonia modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from magonia.seabex_api import SeabexAPI

# Load environment variables
load_dotenv()

# Initialize the API client
client_id = os.getenv("SEABEX_CLIENT_ID")
client_secret = os.getenv("SEABEX_CLIENT_SECRET")
user_id = "f68381cd-a748-47bd-842c-701790b35e3c"

# Create API instance
api = SeabexAPI(client_id, client_secret)
api.set_scopes(["magonia-api"])
api.set_user_id(user_id)
api.authenticate()

# Test function to call the API directly
def test_irrigation_data(field_name, date):
    print(f"\n--- Testing irrigation data for field '{field_name}' on {date} ---")

    # Call the API
    result = api.tools().irrigations().call_tool(
        'check_irrigation_user_data',
        {
            'field_name': field_name,
            'date_of_calculation': date,
        }
    )

    print(f"API Result: {result}")
    return result

# Generate test dates (today and several dates in the past and future)
today = datetime.now().strftime('%Y-%m-%d')
dates = [
    today,
    (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d'),  # 1 month ago
    (datetime.now() - timedelta(days=60)).strftime('%Y-%m-%d'),  # 2 months ago
    (datetime.now() + timedelta(days=30)).strftime('%Y-%m-%d'),  # 1 month from now
    (datetime.now() + timedelta(days=60)).strftime('%Y-%m-%d'),  # 2 months from now
    "2024-01-28",  # Fixed date from previous test
    "2024-05-20",  # Another fixed date from previous test
]

# Test fields
fields = ["CHlewi", "Kalel", "Giblewi1", "Taba1"]

# Run tests
print("=== Starting API Tests ===")
print(f"User ID: {user_id}")
print(f"Today's date: {today}")
print("=" * 50)

# Test each field with each date
for field in fields:
    for date in dates:
        result = test_irrigation_data(field, date)
        print(f"Field: {field}, Date: {date}, Result: {result}")
        time.sleep(1)  # Small delay between requests
    print("-" * 50)

print("\n=== Tests Complete ===")
