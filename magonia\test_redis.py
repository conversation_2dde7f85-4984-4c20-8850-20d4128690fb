from redis import Redis
import redis
import os
 
redis_url = os.getenv("REDIS_URL")
 

# Create a Redis connection
try:
 
    redis_client = Redis.from_url(redis_url)
 

    # Test the connection
    redis_client.ping()
    print("Connected to Redis!")

except redis.AuthenticationError:
    print("Authentication failed: invalid username-password pair or user is disabled.")
except redis.ConnectionError as e:
    print(f"Connection error: {e}")
except Exception as e:
    print(f"An error occurred: {e}")
