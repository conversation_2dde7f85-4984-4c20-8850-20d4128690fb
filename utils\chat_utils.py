def convert_chat_history_to_json(chat_history):
    """Convert a formatted chat history string into a list of message dictionaries."""
    messages = []
    lines = chat_history.strip().splitlines()
    
    for line in lines:
        role, content = line.split(": ", 1)
        messages.append({"role": role, "content": content})
    
    return messages

def convert_json_to_chat_history(messages):
    """Convert a list of message dictionaries into a formatted chat history string."""
    chat_history = ""
    for message in messages:
        chat_history += f"{message['role']}: {message['content']}\n"
    return chat_history.strip()  

def append_to_chat_history(chat_history, prompt, response):
    """Append a new human-AI interaction to the chat history."""
    return f"{chat_history}Human: {prompt}\nAI: {response}\n"