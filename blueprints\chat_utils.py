from flask import Blueprint, jsonify, request
from decorators.token_required import token_required
from magonia.redis_client import redis_client
from magonia.magonia_ai import magonia_instance
from utils.chat_utils import append_to_chat_history
 
chat_blueprint = Blueprint('chat', __name__)

@chat_blueprint.route('/magonia/chat', methods=['POST'])
@token_required
def ask_route():
    data = request.json

    if 'prompt' not in data or 'session_id' not in data:
        return jsonify({"error": "Missing required fields."}), 400

    prompt = data.get('prompt')
    session_id = data['session_id']

    # Retrieve chat history from Redis
    chat_history = redis_client.get(session_id)
    chat_history = chat_history.decode('utf-8') if chat_history else ""

    # Generate response from AI
    response = magonia_instance.ask(chat_history, prompt)

    # Append new interaction to chat history
    updated_chat_history = append_to_chat_history(chat_history, prompt, response)
    redis_client.set(session_id, updated_chat_history)

    return jsonify({'response': response})
