from typing import Optional
from flask import g
from langchain_core.tools import tool

from utils.utils import parse_integer_from_string

@tool
def calculate_total_irrigation_volume_next_x_days(days: Optional[int] = 5) -> dict:
    """
    Calculate the total irrigation volume (in mm or liters) recommended for all fields combined over the next X days.

    USE THIS TOOL WHEN:
    - The user asks about total water needs for all fields
    - The user wants to know the combined irrigation volume for planning
    - The user needs to estimate total water requirements for a period
    - The user asks how much water they'll need in total for irrigation

    DO NOT USE THIS TOOL WHEN:
    - The user asks about a specific field's irrigation needs (use field-specific tools)
    - The user asks about irrigation for a specific date (use date-specific tools)
    - The user asks which fields need irrigation (use field identification tools)
    - The user asks about soil moisture rather than irrigation volume

    EXAMPLE QUERIES:
    - "What is the total irrigation volume needed for all fields in the next 5 days?"
    - "How much water will I need in total for irrigation this week?"
    - "Calculate the total water requirements for all my fields for the next 10 days"
    - "What's the combined irrigation volume I should plan for?"

    Args:
        days (int, optional): The number of future days to calculate total irrigation volume for.
                             Default is 5. Must be a positive integer.

    Returns:
        dict: The total irrigation volume recommended for all fields in the specified period,
              with a breakdown by field if available, or an error message if data retrieval fails.
    """
    try:
        days =  parse_integer_from_string(days)

        if not isinstance(days, int):
            return {"message": "The 'days' parameter must be an integer."}

        response = g.seabex_api.tools().irrigations().call_tool(
            'calculate_total_irrigation_volume_next_x_days',
            {'days': days}
        )

        if isinstance(response, dict) and 'error' in response:
            error_message = response['error']
            return {"message": f"Error: {error_message}"}

        return response

    except Exception as e:
        print(f"Error calculating total irrigation volume: {e}")
        return {
            "message": "I'm sorry, I couldn't calculate the irrigation volume at the moment. Please try again later."
        }
