import os
import random
import string
import time
import traceback
import requests
import json
from dotenv import load_dotenv
import colorama
from colorama import Fore, Style

# Initialize colorama
colorama.init()

# Load environment variables
load_dotenv()

token = os.getenv('AUTH_TOKEN')
base_url = "http://127.0.0.1:8080"

def send_prompt(prompt, session_id, user_id, token=None):
    """Send a prompt to the Magonia API and return the response."""
    url = f"{base_url}/magonia/chat"
    
    data = {
        "session_id": session_id,
        "prompt": prompt,
        "chat_history": "",
        "scopes": ["magonia-api"],
        "user_id": user_id
    }
    
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {token}" if token else ""
    }
    
    try:
        response = requests.post(url, headers=headers, data=json.dumps(data))
        
        if response.status_code != 200:
            print(f"{Fore.RED}Error: {response.status_code}{Style.RESET_ALL}")
            print(f"{Fore.RED}{response.text}{Style.RESET_ALL}")
            return None
        
        return response.json()
    except Exception as e:
        print(f"{Fore.RED}Error: {str(e)}{Style.RESET_ALL}")
        traceback.print_exc()
        return None

# General knowledge questions that should NOT use tools
general_knowledge_questions = [
    "What is the best time to irrigate my fields?",
    "How does drip irrigation work?",
    "What are signs of over-watering?",
    "How can I improve soil moisture retention?",
    "What are the benefits of crop rotation?",
    "How often should I irrigate olive trees?",
    "What's the difference between drip and sprinkler irrigation?",
    "How can I prevent soil erosion in my fields?",
    "What are the best practices for water conservation in agriculture?",
    "How does soil type affect irrigation needs?"
]

# Questions that SHOULD use tools
tool_requiring_questions = [
    "What's the date today?",
    "Do I need to irrigate my field ChleWi today?",
    "Which of my fields needs the most water this week?",
    "What's my name?",
    "When is the next recommended irrigation date for my fields?",
    "How much water should I apply to my fields today?",
    "Which fields have optimal soil moisture levels right now?",
    "What are all my fields?",
    "Will any of my fields exceed water capacity this week?",
    "What's the total irrigation volume needed for all my fields in the next 5 days?"
]

def run_test():
    """Run the test with both types of questions."""
    # Generate a random session ID and user ID
    session_id = ''.join(random.choice(string.ascii_letters + string.digits) for _ in range(10))
    user_id = ''.join(random.choice(string.ascii_letters + string.digits) for _ in range(10))
    
    print(f"{Fore.CYAN}Using session_id: {session_id}{Style.RESET_ALL}")
    print(f"{Fore.CYAN}Using user_id: {user_id}{Style.RESET_ALL}")
    print()
    
    # First, test general knowledge questions
    print(f"{Fore.YELLOW}TESTING GENERAL KNOWLEDGE QUESTIONS (should NOT use tools):{Style.RESET_ALL}")
    print("=" * 80)
    
    for i, question in enumerate(general_knowledge_questions, 1):
        print(f"{Fore.BLUE}[{i}] YOU: {question}{Style.RESET_ALL}")
        response = send_prompt(question, session_id, user_id, token)
        
        if response:
            print(f"{Fore.GREEN}AI: {response}{Style.RESET_ALL}")
            print()
        else:
            print(f"{Fore.RED}Failed to get a response from the AI.{Style.RESET_ALL}")
            print()
        
        time.sleep(1)  # Add a small delay between messages
    
    print("\n" + "=" * 80 + "\n")
    
    # Next, test tool-requiring questions
    print(f"{Fore.YELLOW}TESTING TOOL-REQUIRING QUESTIONS (should use tools):{Style.RESET_ALL}")
    print("=" * 80)
    
    for i, question in enumerate(tool_requiring_questions, 1):
        print(f"{Fore.BLUE}[{i}] YOU: {question}{Style.RESET_ALL}")
        response = send_prompt(question, session_id, user_id, token)
        
        if response:
            print(f"{Fore.GREEN}AI: {response}{Style.RESET_ALL}")
            print()
        else:
            print(f"{Fore.RED}Failed to get a response from the AI.{Style.RESET_ALL}")
            print()
        
        time.sleep(1)  # Add a small delay between messages

if __name__ == "__main__":
    run_test()
