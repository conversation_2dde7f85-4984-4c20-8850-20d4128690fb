from typing import Optional
from flask import g
from langchain_core.tools import tool
from utils.utils import parse_integer_from_string

@tool
def average_soil_water_volume_for_x_days(days: Optional[int] = 0) -> dict:
    """
    Calculate the average soil water volume for all fields for the next X days.

    Args:
        days (int, optional): The number of future days to check for average soil water volume. 
                    Default is 0, which means it will check for today.

    Returns:
        dict: The average soil water volume for all fields or an error message.
    """
    try:
        days = parse_integer_from_string(days)
        
        if not isinstance(days, int):
            return {"message": "The 'days' parameter must be an integer."}
 
        response = g.seabex_api.tools().soils().call_tool(
            'average_soil_water_volume_for_x_days',
            {'days': days}
        )

        if isinstance(response, dict) and 'error' in response:
            error_message = response['error']
            return {"message": f"Error: {error_message}"}

        return response

    except Exception as e:
        print(f"Error finding fields predicted to exceed water capacity: {e}")
        return {
            "message": "I'm sorry, I couldn't find the water capacity data at the moment. Please try again later."
        }
