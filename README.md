# Magonia AI - Agricultural Assistant

<PERSON><PERSON><PERSON> is an AI assistant specialized in irrigation and agriculture, designed to help farmers manage their fields, make irrigation decisions, and learn about best practices in agriculture.

## Features

- **Conversational Interface**: Natural, friendly conversation style rather than formal Q&A
- **Multilingual Support**: Responds in the same language as the user's query (English, French, Tunisian Arabic)
- **Memory System**: Remembers user information across conversations
- **Specialized Tools**: Access to irrigation data, field information, and agricultural recommendations
- **General Knowledge**: Built-in knowledge about agricultural best practices and concepts

## Recent Improvements

### 1. Enhanced Tool Descriptions

All tools now have standardized, detailed descriptions that help the AI make better decisions about which tool to use:

- **USE THIS TOOL WHEN**: Clear guidance on when each tool should be used
- **DO NOT USE THIS TOOL WHEN**: Explicit instructions on when not to use a tool
- **EXAMPLE QUERIES**: Sample user queries that should trigger each tool

### 2. Better Distinction Between Tools and General Knowledge

The system now correctly distinguishes between:
- Questions that require specific user data (uses tools)
- General agricultural knowledge questions (uses built-in knowledge)

### 3. More Conversational Experience

- Natural dialogue flow rather than just question/answer exchanges
- Personalized responses using the user's name
- Follow-up questions to maintain conversation
- Friendly, approachable tone

## Using the System

### Running the Interactive Chat

```bash
python -m magonia.interactive_chat
```

This starts a real-time chat session where you can have a natural conversation with Magonia.

### Running the Conversational Test

```bash
python -m magonia.conversational_test
```

This runs through a series of pre-defined prompts that demonstrate the conversational capabilities.

### Testing General Knowledge vs. Tool Usage

```bash
python -m magonia.general_knowledge_test
```

This tests whether the AI correctly uses tools for specific data questions and built-in knowledge for general questions.

## Types of Questions

### Questions That Use Built-in Knowledge (No Tools)

- "What is the best time to irrigate my fields?"
- "How does drip irrigation work?"
- "What are signs of over-watering?"
- "How can I improve soil moisture retention?"
- "What are the benefits of crop rotation?"

### Questions That Use Tools

- "Do I need to irrigate my field ChleWi today?"
- "What's the date today?"
- "Which of my fields needs the most water this week?"
- "What's my name?"
- "When is the next recommended irrigation date for my fields?"

## Tool Categories

### Time Tools
- `get_current_time_date`: Get the current date and time
- `get_tomorrow`: Get tomorrow's date
- `get_next_week`: Get the date one week from today
- `get_next_month`: Get the date one month from today

### Irrigation Tools
- `check_today_active_irrigation_user`: Get today's irrigation recommendations
- `check_irrigation_need_for_x_days`: Check irrigation needs for the next X days
- `check_irrigation_needs_between_period`: Check irrigation for a specific field between dates
- `check_earliest_irrigation_dates`: Find fields with earliest irrigation dates
- `check_highest_irrigation_requirement`: Find field with highest irrigation requirement

### Field Management Tools
- `get_all_user_areas_with_children`: List all user's fields and areas
- `fields_with_highest_water_requirements_for_x_days`: Find fields needing most water
- `fields_with_highest_evapotranspiration_next_x_days`: Find fields with highest water loss
- `fields_with_optimal_soil_moisture_for_x_days`: Find fields with ideal moisture levels

### Memory Tools
- `add_memory`: Store important information about the user
- `get_memories`: Retrieve stored information about the user
- `delete_memory`: Remove a specific memory
- `clear_memories`: Remove all stored memories for a user

## Development

### Tool Description Template

When creating new tools, follow the standardized format in `magonia/tools/tool_description_template.md`:

```python
@tool
def tool_name(param1: Type, param2: Type = default_value) -> ReturnType:
    """
    [1-2 sentence clear description of what the tool does]

    USE THIS TOOL WHEN:
    - [Specific scenario when this tool should be used]
    - [Another specific scenario when this tool should be used]

    DO NOT USE THIS TOOL WHEN:
    - [Specific scenario when this tool should NOT be used]
    - [Another specific scenario when this tool should NOT be used]

    EXAMPLE QUERIES:
    - "[Example user query that should trigger this tool]"
    - "[Another example user query that should trigger this tool]"

    Args:
        param1 (Type): [Description of parameter]
        param2 (Type, optional): [Description of parameter]. Defaults to [default_value].

    Returns:
        ReturnType: [Description of return value]
    """
```

### Tool Guide

For a comprehensive guide to all tools, see `magonia/tools/TOOL_GUIDE.md`.

## Troubleshooting

If the AI is not behaving as expected:

1. **Using Tools for General Questions**: If the AI tries to use tools for general knowledge questions, check the prompt in `magonia/llm_prompt.py` and ensure the instructions about when to use tools are clear.

2. **Not Using Tools When Needed**: If the AI doesn't use tools for specific data questions, check the tool descriptions to ensure they clearly indicate when each tool should be used.

3. **Memory Issues**: If the AI doesn't remember user information, check the memory system implementation and ensure it's properly storing and retrieving memories.

4. **Language Support**: If the AI doesn't respond in the same language as the query, check the language detection and response generation in the prompt.

## Contributing

When contributing to this project:

1. Follow the tool description template for new tools
2. Run the test scripts to ensure your changes don't break existing functionality
3. Update documentation to reflect your changes
4. Add examples to help users understand how to use new features