# Magonia AI Quick Start Guide

This guide will help you get started with Magonia AI, your agricultural assistant.

## Installation

1. Clone the repository:
   ```bash
   git clone [repository-url]
   cd irrigation-advisor
   ```

2. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

3. Set up environment variables:
   ```bash
   cp .env.example .env
   ```
   Edit the `.env` file to add your authentication token.

## Starting a Conversation

### Interactive Chat

For a natural conversation experience:

```bash
python -m magonia.interactive_chat
```

This opens an interactive terminal where you can chat with <PERSON>gon<PERSON>.

### Test Script

To run through a series of pre-defined conversational prompts:

```bash
python -m magonia.conversational_test
```

## What You Can Ask

### General Agricultural Questions

Magonia has built-in knowledge about agriculture and irrigation. You can ask questions like:

- "What is the best time to irrigate my fields?"
- "How does drip irrigation work?"
- "What are signs of over-watering?"
- "How can I improve soil moisture retention?"
- "What are the benefits of crop rotation?"

### Field-Specific Questions

Magonia can access data about your specific fields. You can ask:

- "Do I need to irrigate my field ChleWi today?"
- "Which of my fields needs the most water this week?"
- "When is the next recommended irrigation date for my fields?"
- "What's the total irrigation volume needed for all my fields in the next 5 days?"

### Date and Time Questions

- "What's the date today?"
- "What will tomorrow's date be?"
- "What's the date next week?"

## Tips for Better Conversations

1. **Be Specific About Fields**: When asking about irrigation needs, mention the specific field name if you're interested in a particular field.

2. **Specify Time Periods**: For forecasts or planning, specify the time period you're interested in (e.g., "next 5 days," "between May 10 and May 22").

3. **Follow-Up Questions**: Feel free to ask follow-up questions to get more details or clarification.

4. **Multiple Languages**: Magonia supports English, French, and Tunisian Arabic. It will respond in the same language you use.

## Troubleshooting

If you encounter issues:

1. **Connection Problems**: Ensure the server is running and your authentication token is correct.

2. **No Data for Fields**: Verify that your fields are properly registered in the system.

3. **Incorrect Responses**: Try rephrasing your question to be more specific.

4. **Language Issues**: Make sure you're using a supported language (English, French, or Tunisian Arabic).

## Need More Help?

For more detailed information, see the full [README.md](README.md) or the [Tool Guide](magonia/tools/TOOL_GUIDE.md).
