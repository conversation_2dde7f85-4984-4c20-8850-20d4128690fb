# Script to clear Redis memory
import os
import redis
import json

# Get Redis URL from environment
redis_url = os.getenv("REDIS_URL")

try:
    # Connect to Redis
    redis_client = redis.Redis.from_url(redis_url)
    
    # Test connection
    redis_client.ping()
    print("Connected to Redis!")
    
    # Get all keys that start with "memory:"
    memory_keys = redis_client.keys("memory:*")
    
    if memory_keys:
        print(f"Found {len(memory_keys)} memory keys:")
        for key in memory_keys:
            key_str = key.decode('utf-8')
            print(f"- {key_str}")
            
            # Get the memories for this key
            memories = redis_client.get(key)
            if memories:
                memories_list = json.loads(memories.decode('utf-8'))
                print(f"  Contains {len(memories_list)} memories")
            
            # Delete the key
            redis_client.delete(key)
            print(f"  Deleted key: {key_str}")
    else:
        print("No memory keys found in Redis.")
    
    # Also clear the in-memory store
    from magonia import memory_store
    memory_store.memory_store.clear()
    print("\nIn-memory store cleared!")
    print("Current memory store content:")
    print(memory_store.memory_store)
    
    print("\nAll memories have been cleared successfully!")
    
except redis.exceptions.ConnectionError as e:
    print(f"Connection error: {e}")
    print("Clearing only the in-memory store...")
    
    # Clear the in-memory store
    from magonia import memory_store
    memory_store.memory_store.clear()
    print("In-memory store cleared!")
    print("Current memory store content:")
    print(memory_store.memory_store)
    
except Exception as e:
    print(f"An error occurred: {e}")
