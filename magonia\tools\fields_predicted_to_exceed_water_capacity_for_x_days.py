from typing import Optional
from flask import g
from langchain_core.tools import tool
from utils.utils import parse_integer_from_string

@tool
def fields_predicted_to_exceed_water_capacity_for_x_days(days: Optional[int] = 0) -> dict:
    """
    Identify fields at risk of over-saturation by predicting which ones will exceed their water capacity in the specified period.

    USE THIS TOOL WHEN:
    - The user asks which fields might get waterlogged on a specific day
    - The user wants to know about potential over-irrigation risks for today
    - The user asks about fields that might exceed water capacity soon
    - The user is concerned about immediate drainage issues in their fields

    DO NOT USE THIS TOOL WHEN:
    - The user asks which fields need irrigation (use irrigation requirement tools)
    - The user asks about fields with low moisture (use low moisture tools)
    - The user asks about a specific field without comparing to others
    - The user asks about historical rather than future water capacity

    EXAMPLE QUERIES:
    - "Which fields might exceed their water capacity today?"
    - "Are any of my fields at risk of becoming waterlogged tomorrow?"
    - "Which areas might have drainage problems right now?"
    - "Identify fields that could get over-saturated today"

    Args:
        days (int, optional): The number of future days to check for water capacity exceedance.
                             Default is 0, which means it will check for today. Must be a non-negative integer.

    Returns:
        dict: A list of fields predicted to exceed their water capacity in the specified period,
              including field names and relevant details, or an error message if data retrieval fails.
    """
    try:
        days = parse_integer_from_string(days)

        if not isinstance(days, int):
            return {"message": "The 'days' parameter must be an integer."}

        response = g.seabex_api.tools().soils().call_tool(
            'fields_predicted_to_exceed_water_capacity_for_x_days',
            {'days': days}
        )

        if isinstance(response, dict) and 'error' in response:
            error_message = response['error']
            return {"message": f"Error: {error_message}"}

        return response

    except Exception as e:
        print(f"Error finding fields predicted to exceed water capacity: {e}")
        return {
            "message": "I'm sorry, I couldn't find the water capacity data at the moment. Please try again later."
        }
