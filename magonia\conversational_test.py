import os
import random
import string
import time
import traceback
from flask import json
import requests
import json
from dotenv import load_dotenv
import colorama
from colorama import Fore, Style

# Initialize colorama
colorama.init()

load_dotenv()

token = os.getenv('AUTH_TOKEN')


def send_prompt(prompt, session_id="session123", user_id="f68381cd-a748-47bd-842c-701790b35e3c", token=None):
    url = "http://127.0.0.1:8080/magonia/chat"
    data = {
        "session_id": session_id,
        "prompt": prompt,
        "chat_history": "",
        "scopes": ["magonia-api"],
        "user_id": user_id
    }
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {token}"
    }
    response = requests.post(url, headers=headers, data=json.dumps(data))
    print(f"{Fore.YELLOW}Status Code: {response.status_code}{Style.RESET_ALL}")

    if response.status_code != 200:
        print("Response Text:", response.text)
        try:
            return response.text
        except Exception as e:
            print(e)
            traceback.print_exc()
            return {
                'error': 'An unexpected error occurred, and the response is not valid JSON.',
                'status_code': response.status_code
            }

    try:
        json_response = response.json()
        print(f"{Fore.GREEN}Response: {json_response}{Style.RESET_ALL}")
        return json_response
    except Exception as e:
        print(e)
        traceback.print_exc()
        return None



# Prompts to send - more conversational style
prompts = [
    "Hi there! My name is Khalil. I'm a farmer from Tunisia.",
    "I'm curious, what's the date today? I've been busy in the fields and lost track.",
    "Thanks! By the way, do you remember my name?",
    "I've been thinking about my irrigation schedule. Which of my fields will need the most water over the next 5 days?",
    "That's interesting. What do you think is the best time of day to irrigate my fields?",
    "I appreciate your help. Can you tell me more about efficient irrigation techniques?",
    "I'm also concerned about water conservation. Any tips for reducing water usage while maintaining crop health?",
    "That makes sense. How does soil type affect irrigation needs?",
    "I've heard about drip irrigation. Is it better than sprinkler systems for olive trees?",
    "You've been really helpful today. I'll try implementing some of these ideas on my farm."
]

# Start the timer
start_time = time.time()
session_id = "session123"
user_id = "f68381cd-a748-47bd-842c-701790b35e3c"
print(f"Using session_id: {session_id}")
print(f"Using user_id: {user_id}")

# Your prompt processing loop
for prompt in prompts:
    print("\033[31m" + "PROMPT : " + "\033[0m" + "\033[32m" + prompt + "\033[0m")
    send_prompt(prompt=prompt, session_id=session_id, user_id=user_id, token=token)



# End the timer
end_time = time.time()


execution_time = end_time - start_time


if execution_time < 1:
    execution_message = f"{execution_time * 1000:.2f} ms"
elif execution_time < 60:
    execution_message = f"{execution_time:.2f} seconds"
else:
    minutes = execution_time // 60
    seconds = execution_time % 60
    execution_message = f"{int(minutes)} minute(s) and {seconds:.2f} seconds"

print(f"{Fore.CYAN}Execution time: {execution_message}{Style.RESET_ALL}")
