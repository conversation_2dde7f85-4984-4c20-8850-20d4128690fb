from datetime import datetime
from typing import Optional, Union
from flask import g
from langchain_core.tools import tool

@tool
def check_highest_evapotranspiration(calculation_date: Optional[Union[datetime, str]] = datetime.now().date()):
    """
    Retrieve the field with the highest real evapotranspiration for the specified date.

    Args:
        calculation_date (Union[datetime, str]]): The date for which to check evapotranspiration.

    Returns:
        str: A summary of the field with the highest real evapotranspiration or an error message.
    """

    # Format the date as a string if it's a datetime object
    if isinstance(calculation_date, datetime):
        calculation_date = calculation_date.strftime('%Y-%m-%d')

    try:
        response = g.seabex_api.tools().irrigations().call_tool(
            'check_highest_evapotranspiration',
            {
                'calculation_date': calculation_date
            }
        )
        
        if isinstance(response, dict) and 'error' in response:
            error_message = response['error']
            return f"Error: {error_message}"
        
        # Process the response
        if response and "field_name" in response and "value" in response:
            field_name = response["field_name"]
            value = response["value"]
            return (
                f"The field with the highest real evapotranspiration on {calculation_date} is "
                f"'{field_name}' with a value of {value}."
            )
        else:
            return "No data available"

    except Exception as e:
        print(f"Error retrieving evapotranspiration data: {e}")
        return (
            "I'm sorry, I couldn't retrieve the evapotranspiration information at the moment. "
            "Please try again later."
        )
