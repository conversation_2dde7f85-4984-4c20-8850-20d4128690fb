

from datetime import datetime
import re
from typing import Optional, Union
from flask import g
from langchain_core.tools import tool

@tool
def check_irrigation_user_data(
    field_name: Optional[str] = None,
    date_of_calculation: Optional[Union[datetime, str]] = None,
    user_id: Optional[str] = None
) -> str:
    """
    Retrieve the irrigation recommendation (in mm) for a specific field on a given date.
    If the field name or date is not provided, it prompts the user for the missing information.

    Args:
        field_name (str): Name of the field.
        date_of_calculation (Optional[Union[datetime, str]]): Date of calculation in 'YYYY-MM-DD' format.
                                                             Defaults to today if not provided.
        user_id (Optional[str]): User ID for authentication. Will be passed to the API.

    Returns:
        str: Irrigation recommendation or a prompt requesting the missing information.
    """

    try:
        from utils.utils import parse_input

        # Check if field_name is a JSON-like string that needs parsing
        if field_name and ('{' in field_name or '=' in field_name or ':' in field_name or '"' in field_name):
            # Parse the tool input
            parsed_data = parse_input(field_name)
            print("Parsed data:", parsed_data)

            # Extract the values from the parsed data
            if parsed_data:
                field_name = str(parsed_data.get('field_name', '')).strip().replace('"', '').replace("'", '')
                if 'date_of_calculation' in parsed_data:
                    date_of_calculation = str(parsed_data.get('date_of_calculation', '')).strip().replace('"', '').replace("'", '')

        # Helper function to extract value after '=' or ':'
        def extract_value(input_str, key_name):
            pattern = f"{key_name}\\s*[:=]\\s*(.*)"
            match = re.match(pattern, input_str)
            return match.group(1).strip() if match else input_str.strip()

        # Handle missing field_name
        if not field_name:
            return "Please provide the field name to proceed with the irrigation recommendation."

        # Clean up field_name if it's still in a format with key-value pairs
        if ',' in field_name:
            parts = field_name.split(',')
            field_name = parts[0].strip()
            if len(parts) > 1 and not date_of_calculation:
                date_of_calculation = parts[1].strip()

        # Further clean field_name if it still has key-value format
        if ':' in field_name or '=' in field_name:
            field_name = extract_value(field_name, "field_name")

        # Clean up any remaining quotes
        field_name = field_name.replace('"', '').replace("'", '')

        if not date_of_calculation:
            return f"Please provide the calculation date for the field '{field_name}'."

        # Clean up date_of_calculation
        if isinstance(date_of_calculation, str):
            if ':' in date_of_calculation or '=' in date_of_calculation:
                date_of_calculation = extract_value(date_of_calculation, "date_of_calculation")
            date_of_calculation = date_of_calculation.replace('"', '').replace("'", '')
        if isinstance(date_of_calculation, datetime):
            date_of_calculation = date_of_calculation.strftime('%Y-%m-%d')

        date_of_calculation = date_of_calculation or datetime.now().strftime('%Y-%m-%d')



        # Print the parameters for debugging
        print(f"Calling API with field_name: '{field_name}', date_of_calculation: '{date_of_calculation}'")

        # Add a timestamp to ensure we're getting fresh data (cache busting)
        cache_buster = datetime.now().isoformat()

        # Prepare the payload with the cache buster and user_id if available
        payload = {
            'field_name': field_name,
            'date_of_calculation': date_of_calculation,
            '_cache_buster': cache_buster  # This will be ignored by the API but ensures a fresh request
        }

        # Add user_id to the payload if provided
        if user_id:
            payload['user_id'] = user_id
            print(f"Added user_id to API call: {user_id}")

        # Log the final payload for debugging
        print(f"final payload {payload}")

        # Make the API call with the cache buster
        irrigation_value = g.seabex_api.tools().irrigations().call_tool(
            'check_irrigation_user_data',
            payload
        )

        try:
            # Check if irrigation_value is None (no data available)
            if irrigation_value is None:
                # Check if the date is in the future
                try:
                    date_obj = datetime.strptime(date_of_calculation, '%Y-%m-%d')
                    today = datetime.now()
                    if date_obj > today:
                        return (
                            f"I don't have irrigation data for your field '{field_name}' on {date_of_calculation} as this is a future date.\n\n"
                            f"Irrigation recommendations are typically only available for current or past dates. Please check again closer to the date or try a different date."
                        )
                except Exception:
                    pass

                # Generic message for missing data
                return (
                    f"I don't have irrigation data for your field '{field_name}' on {date_of_calculation}.\n\n"
                    f"This could be because the date is outside our data range or there might be an issue with the data for this field. "
                    f"Please try a different date or contact support if you believe this is an error."
                )

            # Try to convert irrigation_value to float for comparison
            irrigation_float = float(irrigation_value) if irrigation_value else 0

            if irrigation_float > 0:
                return (
                    f"You need to irrigate your field '{field_name}' with {irrigation_value} mm on {date_of_calculation}.\n\n"
                    f"This recommendation is based on current soil moisture levels, weather conditions, and crop requirements."
                )
            else:
                # Handle zero values explicitly
                return (
                    f"No irrigation is needed for your field '{field_name}' on {date_of_calculation}.\n\n"
                    f"The current soil moisture level is sufficient for your crops. Continue monitoring conditions for changes."
                )
        except (ValueError, TypeError) as e:
            print(f"Error processing irrigation value: {e}")
            return f"Unable to determine irrigation needs for field '{field_name}' on {date_of_calculation} due to data processing issues."

    except Exception as e:
        return (
            "I'm sorry, I couldn't retrieve the irrigation recommendation at the moment. "
            "Please try again later or provide more details."
        )