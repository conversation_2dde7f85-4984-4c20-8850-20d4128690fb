# Magonia AI Tool Guide

This guide provides an overview of the available tools in the Magonia AI system and when to use each one. The tools are organized by category to help you quickly find the right tool for each user query.

## Time-Related Tools

### get_current_time_date
- **Purpose**: Get the current date and time
- **Use when**: User asks about today's date or current time
- **Example queries**: "What's the date today?", "What time is it?"

### get_tomorrow
- **Purpose**: Get tomorrow's date
- **Use when**: User asks about tomorrow's date
- **Example queries**: "What's the date tomorrow?", "What will tomorrow's date be?"

### get_next_week
- **Purpose**: Get the date one week from today
- **Use when**: User asks about next week's date
- **Example queries**: "What's the date next week?", "What will the date be in 7 days?"

### get_next_month
- **Purpose**: Get the date one month from today
- **Use when**: User asks about next month's date
- **Example queries**: "What's the date next month?", "What will the date be in a month from now?"

## Irrigation Tools

### check_today_active_irrigation_user
- **Purpose**: Get today's irrigation recommendations
- **Use when**: User asks about irrigation needs for today
- **Example queries**: "Do I need to irrigate today?", "What are today's irrigation recommendations?"

### check_irrigation_need_for_x_days
- **Purpose**: Check irrigation needs for the next X days
- **Use when**: User asks about irrigation needs for a period in the future
- **Example queries**: "Will I need to irrigate in the next 5 days?", "What are the irrigation needs for the next week?"

### check_irrigation_needs_between_period
- **Purpose**: Check irrigation needs for a specific field between two dates
- **Use when**: User asks about irrigation for a specific field during a date range
- **Example queries**: "Do I need to irrigate my field ChleWi between May 10 and May 22?", "What are the irrigation recommendations for my olive grove from June 1 to June 15?"

### fields_with_highest_water_requirements_for_x_days
- **Purpose**: Identify fields that will require the most water in the coming days
- **Use when**: User asks which fields need the most water
- **Example queries**: "Which fields will require the most water over the next 5 days?", "Which of my fields will need the most irrigation this week?"

## Memory Tools

### add_memory
- **Purpose**: Store important information about the user
- **Use when**: User shares important information that should be remembered
- **Example scenarios**: User mentions their name, preferences, or important details about their fields

### get_memories
- **Purpose**: Retrieve stored information about the user
- **Use when**: You need to recall information from previous conversations
- **Example scenarios**: User asks if you remember something about them, or you need to personalize a response

### delete_memory
- **Purpose**: Remove a specific memory
- **Use when**: User asks to forget specific information
- **Example queries**: "Please forget that my name is Ahmed", "Remove the information about my irrigation preferences"

### clear_memories
- **Purpose**: Remove all stored memories for a user
- **Use when**: User asks to forget all information
- **Example queries**: "Please forget everything about me", "Clear all my data"

## Decision Tree for Tool Selection

When deciding which tool to use, follow this decision process:

1. **Is the query about time or dates?**
   - If about current time/date → use `get_current_time_date`
   - If about tomorrow → use `get_tomorrow`
   - If about next week → use `get_next_week`
   - If about next month → use `get_next_month`

2. **Is the query about irrigation?**
   - If about today's irrigation → use `check_today_active_irrigation_user`
   - If about irrigation for next X days → use `check_irrigation_need_for_x_days`
   - If about irrigation for specific field and date range → use `check_irrigation_needs_between_period`
   - If about which fields need most water → use `fields_with_highest_water_requirements_for_x_days`

3. **Is the query about user information?**
   - If storing new information → use `add_memory`
   - If retrieving stored information → use `get_memories`
   - If removing specific information → use `delete_memory`
   - If removing all information → use `clear_memories`

## Best Practices

1. **Be specific with tool parameters**
   - Provide all required parameters
   - Use the correct format for dates (YYYY-MM-DD)
   - Use exact field names when querying specific fields

2. **Handle tool errors gracefully**
   - If a tool returns an error, explain the issue to the user
   - Suggest alternatives if the requested information isn't available

3. **Use the most specific tool for the job**
   - Don't use general tools when specific ones are available
   - Match the tool to the exact user query

4. **Combine tool results when necessary**
   - Sometimes multiple tools may be needed to fully answer a query
   - Present the combined information in a coherent way

5. **Respect user privacy with memory tools**
   - Only store information that's relevant and useful
   - Don't store sensitive or private information
   - Be transparent about what information is being stored
