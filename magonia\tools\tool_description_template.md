# Tool Description Template

When updating tool descriptions, follow this template to ensure clarity and consistency:

```python
@tool
def tool_name(param1: Type, param2: Type = default_value) -> ReturnType:
    """
    [1-2 sentence clear description of what the tool does]
    
    USE THIS TOOL WHEN:
    - [Specific scenario when this tool should be used]
    - [Another specific scenario when this tool should be used]
    - [etc.]
    
    DO NOT USE THIS TOOL WHEN:
    - [Specific scenario when this tool should NOT be used]
    - [Another specific scenario when this tool should NOT be used]
    - [etc.]
    
    EXAMPLE QUERIES:
    - "[Example user query that should trigger this tool]"
    - "[Another example user query that should trigger this tool]"
    
    Args:
        param1 (Type): [Description of parameter]
        param2 (Type, optional): [Description of parameter]. Defaults to [default_value].
    
    Returns:
        ReturnType: [Description of return value]
    """
```

## Guidelines for Writing Good Tool Descriptions

1. **Be specific and clear** about what the tool does
2. **Include explicit USE/DO NOT USE sections** to help the AI understand when to use the tool
3. **Provide example queries** that would trigger the tool
4. **Be consistent** in formatting across all tools
5. **Specify parameter types and defaults** clearly
6. **Describe return values** in detail
7. **Group related tools** together in the documentation
