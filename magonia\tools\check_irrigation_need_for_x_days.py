from typing import Optional
from flask import g
from langchain_core.tools import tool

from utils.utils import parse_integer_from_string

@tool
def check_irrigation_need_for_x_days(days: Optional[int] = None, user_id: Optional[str] = None) -> str:
    """
    Check if any field will need irrigation in the next X future days and return detailed information.

    USE THIS TOOL WHEN:
    - The user asks if they need to irrigate in the coming days
    - The user asks about irrigation needs for a specific number of days in the future
    - The user wants to plan irrigation for the next few days
    - The user asks which fields will need water in the next X days

    DO NOT USE THIS TOOL WHEN:
    - The user asks only about today's irrigation (use check_today_active_irrigation_user)
    - The user asks about irrigation for a specific date range (use check_irrigation_needs_between_period)
    - The user asks about a specific field (use field-specific tools)
    - The user is asking about soil moisture, not irrigation (use soil moisture tools)

    EXAMPLE QUERIES:
    - "Will I need to irrigate in the next 5 days?"
    - "Do any of my fields need water in the coming week?"
    - "What are the irrigation needs for the next 3 days?"
    - "Which fields will need irrigation in the next few days?"

    Args:
        days (int, optional): The number of future days to check for irrigation needs.
                             Must be a positive integer.
        user_id (str, optional): The ID of the user making the request.
                                This is used for authentication and data retrieval.

    Returns:
        dict: A detailed list of fields with their irrigation values for the specified period,
              or an error message if the data couldn't be retrieved or the input was invalid.
    """
    try:
        # Only parse if days is a string
        if isinstance(days, str):
            days = parse_integer_from_string(days)

        # Ensure days is an integer
        if not isinstance(days, int):
            return {"message": "The 'days' parameter must be an integer."}

        # Set a default value if days is None or 0
        if days is None or days <= 0:
            days = 5

        # Prepare payload with days and user_id if provided
        payload = {'days': days}
        if user_id:
            payload['user_id'] = user_id

        response = g.seabex_api.tools().irrigations().call_tool(
            'check_irrigation_need_for_x_days',
            payload
        )

        if isinstance(response, dict) and 'error' in response:
            error_message = response['error']
            return f"Error: {error_message}"

        # Process the response to make it more user-friendly
        if isinstance(response, dict):
            fields_data = response.get('fields', [])

            # Filter fields that actually need irrigation
            fields_needing_irrigation = [
                field for field in fields_data
                if field.get('total_irrigation_volume', 0) > 0
            ]

            if fields_needing_irrigation:
                # Sort fields by irrigation volume (highest first)
                fields_needing_irrigation.sort(
                    key=lambda x: float(x.get('total_irrigation_volume', 0)),
                    reverse=True
                )

                # Format the response
                result = f"Fields that need irrigation in the next {days} days:\n\n"

                for field in fields_needing_irrigation:
                    field_name = field.get('field_name', 'Unknown field')
                    total_volume = float(field.get('total_irrigation_volume', 0))
                    irrigation_days = field.get('irrigation_days', [])

                    # Format irrigation days
                    days_info = []
                    for day in irrigation_days:
                        date = day.get('date', 'Unknown date')
                        volume = float(day.get('volume', 0))
                        if volume > 0:
                            days_info.append(f"  • {date}: {volume:.2f} mm")

                    days_summary = "\n".join(days_info)

                    result += (
                        f"• {field_name}: {total_volume:.2f} mm total\n"
                        f"{days_summary}\n\n"
                    )

                # Add summary
                result += f"In total, {len(fields_needing_irrigation)} field(s) need irrigation in the next {days} days."
                return result
            else:
                return f"None of your fields need irrigation in the next {days} days based on current forecasts."

        # Fallback for unexpected response format
        return f"Irrigation forecast for the next {days} days: {response}"

    except Exception as e:
        print(f"Error checking future irrigation need: {e}")
        return "I'm sorry, I couldn't check irrigation needs at the moment. Please try again later."
