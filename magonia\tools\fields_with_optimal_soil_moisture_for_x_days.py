from typing import Optional
from flask import g
from langchain_core.tools import tool

from utils.utils import parse_integer_from_string

@tool
def fields_with_optimal_soil_moisture_for_x_days(days: Optional[int] = 5, user_id: Optional[str] = None) -> dict:
    """
    Identify fields predicted to maintain optimal soil moisture levels without irrigation for the next X days.

    USE THIS TOOL WHEN:
    - The user asks which fields have ideal soil moisture
    - The user wants to know which fields are in good condition water-wise
    - The user asks about fields that don't need immediate attention
    - The user wants to identify fields with balanced water content

    DO NOT USE THIS TOOL WHEN:
    - The user asks which fields need irrigation (use irrigation requirement tools)
    - The user asks about a specific field's moisture level (use field-specific tools)
    - The user asks about irrigation volumes (use irrigation volume tools)
    - The user asks about fields with low moisture (use low moisture tools)

    EXAMPLE QUERIES:
    - "Which fields will have optimal soil moisture in the next 5 days?"
    - "Are any of my fields at ideal moisture levels for the coming week?"
    - "Which areas have good soil moisture conditions?"
    - "Tell me which fields have balanced water content for the next few days"

    Args:
        days (int, optional): The number of future days to check for optimal soil moisture.
                             Defaults to 5. Must be a positive integer.
        user_id (str, optional): The ID of the user making the request.
                                This is used for authentication and data retrieval.

    Returns:
        dict: A list of fields predicted to have optimal soil moisture levels in the specified period,
              including field names and moisture details, or an error message if data retrieval fails.
    """
    try:
        # Only parse if days is a string
        if isinstance(days, str):
            days = parse_integer_from_string(days)

        # Ensure days is an integer
        if not isinstance(days, int):
            days = 5  # Default to 5 days if not a valid integer
            return {"message": "The 'days' parameter must be an integer. Using default value of 5 days."}

        # Prepare payload with days and user_id if provided
        payload = {'days': days}
        if user_id:
            payload['user_id'] = user_id

        response = g.seabex_api.tools().soils().call_tool(
            'fields_with_optimal_soil_moisture_for_x_days',
            payload
        )

        if isinstance(response, dict) and 'error' in response:
            error_message = response['error']
            return {"message": f"Error: {error_message}"}

        return response

    except Exception as e:
        print(f"Error retrieving optimal soil moisture fields: {e}")
        return {
            "message": "I'm sorry, I couldn't retrieve the optimal soil moisture information at the moment. Please try again later."
        }
