from flask import Blueprint, jsonify, request
from decorators.token_required import token_required
from magonia.redis_client import redis_client
from magonia.magonia_ai import magonia_instance
from utils.chat_utils import append_to_chat_history
from magonia import memory_store
import redis
import json
import re

chat_blueprint = Blueprint('chat', __name__)

@chat_blueprint.route('/magonia/chat', methods=['POST'])
@token_required
def ask_route():
    data = request.json

    if 'prompt' not in data or 'session_id' not in data:
        return jsonify({"error": "Missing required fields."}), 400

    prompt = data.get('prompt')
    session_id = data['session_id']
    user_id = data.get('user_id')  # Get user_id from request data

    chat_history = redis_client.get(session_id)
    chat_history = chat_history.decode('utf-8') if chat_history else ""

    # Check for name introduction and store it in memory
    name_patterns = [
        r"(?i)my name is (\w+)",
        r"(?i)i am (\w+)",
        r"(?i)i'm (\w+)",
        r"(?i)call me (\w+)",
        r"(?i)je m'appelle (\w+)",
        r"(?i)je suis (\w+)"
    ]

    user_name = None
    for pattern in name_patterns:
        name_match = re.search(pattern, prompt)
        if name_match:
            user_name = name_match.group(1)
            break

    if user_name and user_id:
        try:
            # Create memory key for this user
            memory_key = f"memory:{user_id}"

            # Get existing memories or create new list
            existing_memories = redis_client.get(memory_key)
            if existing_memories:
                memories = json.loads(existing_memories.decode('utf-8'))
            else:
                memories = []

            # Remove any existing name memories
            memories = [m for m in memories if not m.startswith("User's name is")]

            # Add new name memory
            name_memory = f"User's name is {user_name}"
            memories.append(name_memory)

            # Save updated memories
            try:
                redis_client.set(memory_key, json.dumps(memories))
                print(f"Stored memory in Redis: {name_memory}")
            except redis.exceptions.ReadOnlyError:
                # Fallback to in-memory store
                memory_store.store_memory(user_id, name_memory)
                print(f"Stored memory in memory_store: {name_memory}")
            except Exception as e:
                # Fallback to in-memory store
                memory_store.store_memory(user_id, name_memory)
                print(f"Warning: Redis error, using memory_store: {str(e)}")
        except Exception as e:
            print(f"Error processing name introduction: {str(e)}")

    # Check if user is asking for their name
    name_question_patterns = [
        r"(?i)what('s| is) my name",
        r"(?i)who am i",
        r"(?i)do you (know|remember) (my name|who i am)",
        r"(?i)comment je m'appelle",
        r"(?i)quel est mon nom"
    ]

    is_name_question = any(re.search(pattern, prompt) for pattern in name_question_patterns)

    # If asking for name, check if we have it in memory
    if is_name_question and user_id:
        # First try Redis
        remembered_name = None
        try:
            memory_key = f"memory:{user_id}"
            existing_memories = redis_client.get(memory_key)

            if existing_memories:
                memories = json.loads(existing_memories.decode('utf-8'))
                name_memories = [m for m in memories if m.startswith("User's name is")]

                if name_memories:
                    # Extract name from memory
                    name_memory = name_memories[0]
                    remembered_name = name_memory.replace("User's name is ", "")
                    print(f"Found name in Redis: {remembered_name}")
        except Exception as e:
            print(f"Error checking for name in Redis: {str(e)}")

        # If not found in Redis, try in-memory store
        if not remembered_name:
            in_memory_memories = memory_store.get_memories(user_id)
            name_memories = [m for m in in_memory_memories if m.startswith("User's name is")]

            if name_memories:
                name_memory = name_memories[0]
                remembered_name = name_memory.replace("User's name is ", "")
                print(f"Found name in memory_store: {remembered_name}")

        # If we found the name in either store, add it to the context
        if remembered_name:
            # Add a special note to make sure the AI uses this information
            chat_history += f"\nIMPORTANT: The user's name is {remembered_name}. Make sure to use this in your response.\n"

    # Pass user_id to the ask method for memory retrieval
    response = magonia_instance.ask(chat_history, prompt, user_id)

    updated_chat_history = append_to_chat_history(chat_history, prompt, response)

    # Try to save chat history to Redis, but continue if it fails
    try:
        redis_client.set(session_id, updated_chat_history)
    except redis.exceptions.ReadOnlyError:
        print("Warning: Redis is in read-only mode. Chat history not saved.")
    except Exception as e:
        print(f"Warning: Could not save chat history: {str(e)}")

    print(response)
    return jsonify(response)
