from typing import Optional
from flask import g
from langchain_core.tools import tool

from utils.utils import parse_integer_from_string

@tool
def total_water_consumption_predicted_for_each_field_x_days(days: Optional[int] = 0) -> dict:
    """
    Calculate the total water consumption predicted for each field over the next X days.

    Args:
        days (int, optional): The number of future days to check for water consumption. 
                    Default is 0 (today).

    Returns:
        dict: The total water consumption predicted for each field or an error message.
    """
    try:
        days = parse_integer_from_string(days)
        
        if not isinstance(days, int) or days < 0:
            return {"message": "The 'days' parameter must be an integer greater than or equal to 0."}

        response = g.seabex_api.tools().irrigations().call_tool(
            'total_water_consumption_predicted_for_each_field_x_days',
            {'days': days}
        )

        if isinstance(response, dict) and 'error' in response:
            error_message = response['error']
            return {"message": f"Error: {error_message}"}

        return response

    except Exception as e:
        print(f"Error calculating total water consumption: {e}")
        return {
            "message": "I'm sorry, I couldn't calculate the total water consumption at the moment. Please try again later."
        }
