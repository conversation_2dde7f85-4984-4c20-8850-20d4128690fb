import os
from langchain.agents import Agent<PERSON>xecutor, create_react_agent
from langchain_openai import ChatOpenAI
from magonia.gpt4o_direct import gpt4o_direct
from magonia.tools.average_soil_water_volume_for_x_days import average_soil_water_volume_for_x_days
from magonia.tools.calculate_total_irrigation_volume_next_x_days import calculate_total_irrigation_volume_next_x_days
from magonia.tools.check_irrigation_need_for_x_days import check_irrigation_need_for_x_days
from magonia.tools.advise_stop_over_irrigation import advise_stop_over_irrigation
from magonia.tools.check_highest_irrigation_requirement import check_highest_irrigation_requirement
from magonia.tools.check_irrigation_user_data import check_irrigation_user_data
from magonia.tools.fields_exceeding_water_capacity_for_x_days import fields_exceeding_water_capacity_for_x_days
from magonia.tools.fields_with_highest_evapotranspiration_next_x_days import fields_with_highest_evapotranspiration_next_x_days
from magonia.tools.fields_with_highest_water_requirements_for_x_days import fields_with_highest_water_requirements_for_x_days
from magonia.tools.fields_with_optimal_soil_moisture_for_x_days import fields_with_optimal_soil_moisture_for_x_days
from magonia.tools.find_fields_no_irrigation_needs_for_x_days import find_fields_no_irrigation_needs_for_x_days
from magonia.tools.fields_predicted_to_exceed_water_capacity_for_x_days import fields_predicted_to_exceed_water_capacity_for_x_days
from magonia.tools.get_all_user_areas_with_children import get_all_user_areas_with_children
from magonia.tools.get_current_irrigation_status import get_current_irrigation_status
from magonia.tools.get_lowest_soil_water_volume import get_lowest_soil_water_volume
from magonia.tools.lookup_document_tool import lookup_document_tool
from magonia.tools.check_today_active_irrigation_user import check_today_active_irrigation_user
from magonia.tools.check_irrigation_needs_between_period import check_irrigation_needs_between_period
from magonia.tools.check_soil_water_volume import check_soil_water_volume
from magonia.tools.check_earliest_irrigation_dates import check_earliest_irrigation_dates
from magonia.tools.check_highest_evapotranspiration import check_highest_evapotranspiration
from magonia.tools.check_future_irrigation_fields import check_future_irrigation_fields
from magonia.tools.predicted_water_consumption_rate_for_x_days import predicted_water_consumption_rate_for_x_days
from magonia.tools.time import get_current_time_date, get_next_month, get_next_week, get_tomorrow
from magonia.tools.memory_tools import add_memory, get_memories, delete_memory, clear_memories
from magonia.llm_prompt import prompt
from datetime import datetime

from magonia.tools.total_water_consumption_predicted_for_each_field_x_days import total_water_consumption_predicted_for_each_field_x_days

# Initialize OpenAI client
openai_api_key = os.getenv("OPENAI_API_KEY")
oai_client = ChatOpenAI(model="gpt-4o", api_key=openai_api_key)


# Get the current date and time
today = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

class Magonia:
    def __init__(self, verbose=False):
        self.tools = [
                      get_current_time_date,
                      get_next_month,
                      get_next_week,
                      get_tomorrow,
                      lookup_document_tool,
                      check_irrigation_user_data,
                      check_today_active_irrigation_user,
                      check_irrigation_needs_between_period,
                      check_soil_water_volume,
                      check_earliest_irrigation_dates,
                      check_highest_evapotranspiration,
                      check_future_irrigation_fields,
                      check_highest_irrigation_requirement,
                      advise_stop_over_irrigation,
                      get_lowest_soil_water_volume,
                      check_irrigation_need_for_x_days,
                      calculate_total_irrigation_volume_next_x_days,
                      find_fields_no_irrigation_needs_for_x_days,
                      predicted_water_consumption_rate_for_x_days,
                      fields_with_highest_water_requirements_for_x_days,
                      fields_with_optimal_soil_moisture_for_x_days,
                      fields_with_highest_evapotranspiration_next_x_days,
                      fields_exceeding_water_capacity_for_x_days,
                      get_current_irrigation_status,
                      fields_predicted_to_exceed_water_capacity_for_x_days,
                      average_soil_water_volume_for_x_days,
                      total_water_consumption_predicted_for_each_field_x_days,
                      get_all_user_areas_with_children,
                      # Memory tools
                      add_memory,
                      get_memories,
                      delete_memory,
                      clear_memories
                    ]

        openai_api_key = os.getenv("OPENAI_API_KEY")
        self.llm = ChatOpenAI(model="gpt-4o", api_key=openai_api_key)

        self.agent = create_react_agent(self.llm, self.tools, prompt=prompt)
        # Configure AgentExecutor with optimized settings to prevent excessive tool retries
        self.agent_executor = AgentExecutor(
            agent=self.agent,
            tools=self.tools,
            verbose=verbose,
            handle_parsing_errors=True,
            max_iterations=1,  # Limit the number of iterations to prevent excessive retries
            early_stopping_method="force"  # Force stop when limits are reached
        )
    def set_language(self, language: str):
        """Set the conversation's language."""
        self.selected_language = language

    def create_tool_tracker(self):
        """Create a wrapper for tools to prevent multiple calls to the same tool."""
        # Dictionary to track which tools have been called
        self.called_tools = {}

        # Create a dictionary to track tool usage
        tool_usage = {}

        # Create a tool tracking function that wraps all tool calls
        def tool_tracker(tool_name, tool_input):
            # Check if this tool has been called before
            if tool_name in tool_usage:
                print(f"Tool {tool_name} has already been called. Preventing duplicate call.")
                return f"This tool has already been used once in this conversation. To optimize response time, I'll use the information already provided or try a different approach."

            # Mark this tool as called
            tool_usage[tool_name] = True

            # Find the original tool
            for tool in self.tools:
                if tool.name == tool_name:
                    # Call the original tool
                    return tool.func(tool_input)

            # If tool not found
            return f"Tool {tool_name} not found."

        # Create the agent with tool tracking
        from langchain.agents import Tool

        # Create new tool objects that use our tracking function
        wrapped_tools = []
        for tool in self.tools:
            # Create a specific function for this tool to avoid lambda closure issues
            def create_tool_func(tool_name):
                def tool_func(input_str):
                    return tool_tracker(tool_name, input_str)
                return tool_func

            wrapped_tool = Tool(
                name=tool.name,
                func=create_tool_func(tool.name),
                description=tool.description
            )
            wrapped_tools.append(wrapped_tool)

        return wrapped_tools

    def ask(self, chat_history: str, question: str, user_id: str = None):
        """Ask a question and get a response."""
        # Reset the called tools for each new question
        self.called_tools = {}

        # Use GPT-4o direct with tool calling
        try:
            print(f"Using GPT-4o direct with tool calling for question: {question}")
            return gpt4o_direct.ask(question, chat_history, user_id)
        except Exception as gpt4o_e:
            print(f"GPT-4o direct failed: {str(gpt4o_e)}")
            import traceback
            traceback.print_exc()

            # Provide a simple fallback response
            return "I'm sorry, I encountered an issue while processing your request. Please try again."


magonia_instance = Magonia(verbose=True)
