version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        PORT: ${PORT}
    ports:
      - "${PORT}:${PORT}"
    environment:
      - PORT=${PORT}
    volumes:
      - .:/app
    command: poetry run python -m magonia.main --host 0.0.0.0
    env_file:
      - .env

# Uncomment and configure Redis if needed for local testing
#   redis:
#     image: "redis/redis-stack-server:latest"
#     ports:
#       - "6379:6379"
#     environment:
#       - REDIS_PASSWORD=PZzX9HGMs2oI9K1kH
#     command: ["redis-server", "--appendonly", "yes", "--requirepass", "PZzX9HGMs2oI9K1kH"]
#     volumes:
#       - redis-data:/data

# volumes:
#   redis-data:
