import re
from datetime import datetime
from typing import Optional, Union
from flask import g
from langchain_core.tools import tool

@tool
def check_earliest_irrigation_dates(date_of_calculation: Optional[any] = None) -> str:
    """
    Retrieve the fields with the earliest upcoming recommended irrigation dates to help with irrigation planning.

    USE THIS TOOL WHEN:
    - The user asks which fields need irrigation first
    - The user wants to know the next irrigation dates for their fields
    - The user is planning their irrigation schedule and needs to prioritize fields
    - The user asks when they should start irrigating

    DO NOT USE THIS TOOL WHEN:
    - The user asks about today's irrigation needs (use check_today_active_irrigation_user)
    - The user asks about a specific field (use field-specific tools)
    - The user asks about irrigation volumes (use volume-specific tools)
    - The user asks about irrigation for a specific date range (use check_irrigation_needs_between_period)

    EXAMPLE QUERIES:
    - "Which field needs irrigation first?"
    - "What are the earliest irrigation dates for my fields?"
    - "When should I start irrigating my fields?"
    - "Which fields have the soonest irrigation recommendations?"

    Args:
        date_of_calculation (any, optional): The reference date for calculations.
                                           Defaults to today if not provided.

    Returns:
        str: Detailed information about the fields with the earliest irrigation dates,
             including field names and dates, or an error message if data retrieval fails.
    """
    try:

        response = g.seabex_api.tools().irrigations().call_tool(
            'check_earliest_irrigation_dates'
        )

        if isinstance(response, dict) and 'error' in response:
            error_message = response['error']
            return f"Error: {error_message}"


        if response:
            return response
        else:
            return "No upcoming irrigation dates found."

    except Exception as e:
        print(e)
        return (
            "I'm sorry, I couldn't retrieve the irrigation dates at the moment. "
            "Please try again later or provide more details."
        )
