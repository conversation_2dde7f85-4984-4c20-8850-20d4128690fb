from functools import wraps
from flask import request, jsonify
from configs.Config import Config

def token_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        auth_header = request.headers.get('Authorization')
        if auth_header:
            try:
                token = auth_header.split(" ")[1]
            except IndexError:
                return jsonify({"error": "Unauthorized"}), 401

            if not validate_token(token):
                return jsonify({"error": "Unauthorized"}), 401
            return f(*args, **kwargs)
        else:
            return jsonify({"error": "Unauthorized"}), 401
    return decorated_function

def validate_token(token):
    """Function to validate the token."""
    return token == Config.AUTH_TOKEN
