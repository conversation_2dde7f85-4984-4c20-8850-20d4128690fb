import requests
import time
import random
from datetime import datetime

class SeabexAPI:
    def __init__(self, client_id, client_secret):
        self.base_url = 'https://back.seabex.com'
        self.client_id = client_id
        self.client_secret = client_secret
        self.access_token = None
        self.token_expires_at = 0
        self.scopes = []
        self.user_id = None
        self.tool_type = None
        self.category = None

    def tools(self):
        """Set the tool category."""
        self.tool_type = 'tools'
        return self

    def areas(self):
        """Set the areas category under tools."""
        self.category = 'areas'
        return self


    def irrigations(self):
        """Set the irrigation category under tools."""
        self.category = 'irrigations'
        return self

    def soils(self):
        """Set the soils category under tools."""
        self.category = 'soils'
        return self

    def set_scopes(self, scopes):
        """Set the scopes for the access token request."""
        if isinstance(scopes, list):
            self.scopes = scopes
        return self

    def set_user_id(self, user_id):
        """Set the user ID for API calls."""
        self.user_id = user_id
        return self

    def _get_access_token(self):
        """Request a new access token."""
        url = f'{self.base_url}/oauth/token'
        data = {
            'grant_type': 'client_credentials',
            'client_id': self.client_id,
            'client_secret': self.client_secret,
            'scope': ','.join(self.scopes),
        }

        response = requests.post(url, data=data)
        if response.status_code == 200:
            token_data = response.json()
            self.access_token = token_data.get('access_token')
            self.token_expires_at = time.time() + token_data.get('expires_in', 3600)
        else:
            print("Failed to obtain access token:", response.status_code, response.text)
            self.access_token = None

    def default_headers(self):
        return {
            'Authorization': f'Bearer {self.access_token}',
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        }

    def authenticate(self):
        """Authenticate and obtain the access token."""
        if not self.access_token or time.time() >= self.token_expires_at:
            self._get_access_token()
        else:
            print("Access token already obtained and valid.")

    def _prepare_payload(self, payload : dict = {}):
        """Prepare the payload by adding user_id and a cache-busting timestamp."""
        # Generate a cache-busting timestamp with millisecond precision and random component
        random_component = ''.join(random.choices('abcdefghijklmnopqrstuvwxyz0123456789', k=8))
        cache_buster = f"{datetime.now().isoformat()}_{random_component}"

        # Create the final payload with user_id and cache buster
        final_payload = {
            'user_id': self.user_id,
            '_cache_buster': cache_buster,  # This will be ignored by the API but ensures a fresh request
            **payload
        }

        # Log that we're using a cache buster to ensure fresh data
        print(f"🔄 Using cache buster to ensure fresh API data: {cache_buster}")
        print(f"📦 Final payload: {final_payload}")

        return final_payload
    def _post_request(self, url, payload : dict = {}):

        payload = self._prepare_payload(payload)

        response = requests.post(url, headers=self.default_headers(), json=payload)

        if response.status_code != 200:
            try:
                return response.json()
            except Exception as e:
                print(e)
                return {
                    'error': 'An unexpected error occurred, and the response is not valid JSON.',
                    'status_code': response.status_code
                }

        try:
            json_response = response.json()
            print(json_response)
            return json_response.get('data')
        except Exception as e:
            print(e)
            return None

    def call_tool(self, method: str, payload: dict = {}):
        """Call the API tool with the set tool type, category, and method."""
        if not self.tool_type or not self.category:
            raise ValueError("Tool type and category must be set before calling the tool.")

        url = f"{self.base_url}/api/magonia/{self.tool_type}/{self.category}/{method}"

        # Log that we're making a fresh API call
        print(f"🌐 Making fresh API call to: {method}")
        print(f"🔗 URL: {url}")
        print(f"📋 Payload: {payload}")
        print(self.access_token)
        # Make the request with cache-busting
        response = self._post_request(url, payload)

        # Log the response
        print(f"✅ Received fresh data from API for: {method}")

        return response
