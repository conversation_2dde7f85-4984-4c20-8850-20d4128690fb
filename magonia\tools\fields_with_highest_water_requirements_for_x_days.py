from typing import Optional
from flask import g
from langchain_core.tools import tool

from utils.utils import parse_integer_from_string

@tool
def fields_with_highest_water_requirements_for_x_days(days: Optional[int] = 5) -> dict:
    """
    Identify and rank fields that will require the most water over the next X days to help with irrigation planning.

    USE THIS TOOL WHEN:
    - The user asks which fields will need the most water in the coming days
    - The user wants to prioritize irrigation resources across multiple fields
    - The user needs to plan water allocation for a specific period
    - The user asks about maximum water requirements for a time period

    DO NOT USE THIS TOOL WHEN:
    - The user asks about a specific field's irrigation needs (use field-specific tools)
    - The user asks only about today's irrigation (use check_today_active_irrigation_user)
    - The user asks about soil moisture rather than irrigation (use soil moisture tools)
    - The user asks about irrigation for a specific date (use date-specific tools)

    EXAMPLE QUERIES:
    - "Which fields will require the most water over the next 5 days?"
    - "Which of my fields will need the most irrigation this week?"
    - "How should I prioritize my irrigation resources for the next few days?"
    - "What are the fields with highest water requirements for the coming week?"

    Args:
        days (int, optional): The number of future days to check for water requirements.
                             Defaults to 5. Must be a positive integer.

    Returns:
        dict: A ranked list of fields requiring the most water, including field names and
              water volumes, or an error message if data retrieval fails.
    """
    try:
        # Only parse if days is a string
        if isinstance(days, str):
            days = parse_integer_from_string(days)

        # Ensure days is an integer
        if not isinstance(days, int):
            return {"message": "The 'days' parameter must be an integer."}

        # Set a default value if days is None or 0
        if days is None or days <= 0:
            days = 5

        response = g.seabex_api.tools().irrigations().call_tool(
            'fields_with_highest_water_requirements_for_x_days',
            {'days': days}
        )

        if isinstance(response, dict) and 'error' in response:
            error_message = response['error']
            return {"message": f"Error: {error_message}"}


        return response

    except Exception as e:
        print(f"Error retrieving water requirements: {e}")
        return {
            "message": "I'm sorry, I couldn't retrieve the water requirements at the moment. Please try again later."
        }
