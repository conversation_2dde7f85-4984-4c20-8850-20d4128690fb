from typing import Optional
from flask import g
from langchain_core.tools import tool

from utils.utils import parse_integer_from_string

@tool
def predicted_water_consumption_rate_for_x_days(days:  Optional[int] = 5) -> dict:
    """
    Get the predicted water consumption rate for each field over the next X days.

    Args:
        days (int, optional): The number of future days to check for water consumption rates. Defaults to 5.

    Returns:
        dict: The predicted water consumption rates for each field or an error message.
    """
    try:
        days =  parse_integer_from_string(days)
        
        if not isinstance(days, int):
            return {"message": "The 'days' parameter must be an integer."}
        
        response = g.seabex_api.tools().irrigations().call_tool(
            'predicted_water_consumption_rate_for_x_days',
            {'days': days}
        )

        if isinstance(response, dict) and 'error' in response:
            error_message = response['error']
            return {"message": f"Error: {error_message}"}
        
        return response

    except Exception as e:
        print(f"Error retrieving water consumption rates: {e}")
        return {
            "message": "I'm sorry, I couldn't retrieve the water consumption rates at the moment. Please try again later."
        }
