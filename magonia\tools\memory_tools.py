import json
from typing import List, Optional
from langchain_core.tools import tool
from flask import g
from magonia.redis_client import redis_client
from magonia import memory_store
import redis

@tool
def add_memory(memory: str, user_id: Optional[str] = None) -> str:
    """
    Add a new memory to the user's memory store for persistent storage across conversations.

    USE THIS TOOL WHEN:
    - You need to remember important information about the user for future conversations
    - The user shares personal preferences that should be remembered
    - The user provides information about their fields or crops that should be stored
    - You want to store information that will be useful in future interactions

    DO NOT USE THIS TOOL WHEN:
    - The information is only relevant to the current conversation
    - The information is sensitive or private (like passwords)
    - You're just acknowledging what the user said without needing to store it
    - The information is already stored in the user's memories

    EXAMPLE QUERIES:
    - When user says: "My name is <PERSON>"
    - When user says: "I prefer to irrigate in the morning"
    - When user says: "My main crop is olives"
    - When user shares important preferences or personal details

    Args:
        memory (str): The memory text to store. Should be concise and meaningful.
        user_id (str, optional): The user ID to associate with the memory. If not provided,
                                uses the current user ID from the request context.

    Returns:
        str: Confirmation message indicating whether the memory was stored successfully
    """
    if not memory:
        return "Error: Memory text cannot be empty."

    # Get user_id from context if not provided
    if not user_id:
        try:
            user_id = g.seabex_api.user_id
        except (AttributeError, KeyError):
            return "Error: No user ID available. Please provide a user_id parameter."

    # Create memory key for this user
    memory_key = f"memory:{user_id}"

    # Get existing memories or create new list
    existing_memories = redis_client.get(memory_key)
    if existing_memories:
        memories = json.loads(existing_memories.decode('utf-8'))
    else:
        memories = []

    # Add new memory if it doesn't already exist
    if memory not in memories:
        memories.append(memory)
        try:
            redis_client.set(memory_key, json.dumps(memories))
            return f"Memory stored successfully in Redis: '{memory}'"
        except redis.exceptions.ReadOnlyError:
            # Fallback to in-memory store
            memory_store.store_memory(user_id, memory)
            return f"Memory stored successfully in memory_store: '{memory}'"
        except Exception as e:
            # Fallback to in-memory store
            memory_store.store_memory(user_id, memory)
            return f"Memory stored in memory_store (Redis error: {str(e)}): '{memory}'"
    else:
        return f"This memory already exists: '{memory}'"

@tool
def get_memories(user_id: Optional[str] = None) -> List[str]:
    """
    Retrieve all stored memories for a user to access information from previous conversations.

    USE THIS TOOL WHEN:
    - You need to recall information about the user from previous conversations
    - You're unsure if you have certain information about the user
    - The user asks what information you have stored about them
    - You need to check if specific preferences or details were previously stored

    DO NOT USE THIS TOOL WHEN:
    - You already have the information you need in the current conversation
    - The user is clearly providing new information that contradicts old memories
    - You're just responding to a simple query that doesn't require personal context

    EXAMPLE QUERIES:
    - When user asks: "Do you remember my name?"
    - When user asks: "What information do you have about me?"
    - When you need to personalize a response based on previous interactions
    - When you need to check if you already have certain information

    Args:
        user_id (str, optional): The user ID to retrieve memories for. If not provided,
                                uses the current user ID from the request context.

    Returns:
        List[str]: List of all stored memories for the user, or an empty list if none exist
    """
    # Get user_id from context if not provided
    if not user_id:
        try:
            user_id = g.seabex_api.user_id
        except (AttributeError, KeyError):
            return "Error: No user ID available. Please provide a user_id parameter."

    # First try to get memories from Redis
    memories = []
    try:
        # Create memory key for this user
        memory_key = f"memory:{user_id}"

        # Get existing memories from Redis
        existing_memories = redis_client.get(memory_key)
        if existing_memories:
            memories = json.loads(existing_memories.decode('utf-8'))
            print(f"Retrieved {len(memories)} memories from Redis for user {user_id}")
    except Exception as e:
        print(f"Error retrieving memories from Redis: {str(e)}")

    # If no memories found in Redis or there was an error, try the in-memory store
    if not memories:
        try:
            in_memory_memories = memory_store.get_memories(user_id)
            if in_memory_memories:
                memories = in_memory_memories
                print(f"Retrieved {len(memories)} memories from memory_store for user {user_id}")
        except Exception as e:
            print(f"Error retrieving memories from memory_store: {str(e)}")

    return memories

@tool
def delete_memory(memory: str, user_id: Optional[str] = None) -> str:
    """
    Delete a specific memory from the user's memory store when it's no longer needed or relevant.

    USE THIS TOOL WHEN:
    - The user asks to forget specific information
    - The user provides updated information that contradicts an old memory
    - The user explicitly requests to remove certain details
    - You need to remove outdated or incorrect information

    DO NOT USE THIS TOOL WHEN:
    - The user wants to clear all memories (use clear_memories instead)
    - The user is just providing new information (use add_memory instead)
    - The user hasn't explicitly asked to forget something
    - The information might still be useful in future conversations

    EXAMPLE QUERIES:
    - "Please forget that my name is Ahmed"
    - "Remove the information about my irrigation preferences"
    - "Delete what I told you about my field location"
    - "I don't want you to remember that I grow olives"

    Args:
        memory (str): The exact memory text to delete. Must match the stored memory exactly.
        user_id (str, optional): The user ID to delete the memory for. If not provided,
                                uses the current user ID from the request context.

    Returns:
        str: Confirmation message indicating whether the memory was successfully deleted
             or that the memory wasn't found
    """
    if not memory:
        return "Error: Memory text cannot be empty."

    # Get user_id from context if not provided
    if not user_id:
        try:
            user_id = g.seabex_api.user_id
        except (AttributeError, KeyError):
            return "Error: No user ID available. Please provide a user_id parameter."

    # Create memory key for this user
    memory_key = f"memory:{user_id}"

    # Get existing memories
    existing_memories = redis_client.get(memory_key)
    if not existing_memories:
        return "No memories found for this user."

    memories = json.loads(existing_memories.decode('utf-8'))

    # Remove memory if it exists
    if memory in memories:
        memories.remove(memory)
        try:
            redis_client.set(memory_key, json.dumps(memories))
            # Also remove from in-memory store if it exists there
            memory_store.delete_memory(user_id, memory)
            return f"Memory deleted successfully: '{memory}'"
        except redis.exceptions.ReadOnlyError:
            # Fallback to in-memory store
            memory_store.delete_memory(user_id, memory)
            return f"Memory deleted from memory_store (Redis is read-only): '{memory}'"
        except Exception as e:
            # Fallback to in-memory store
            memory_store.delete_memory(user_id, memory)
            return f"Memory deleted from memory_store (Redis error: {str(e)}): '{memory}'"
    else:
        # Check if memory exists in in-memory store
        in_memory_memories = memory_store.get_memories(user_id)
        if memory in in_memory_memories:
            memory_store.delete_memory(user_id, memory)
            return f"Memory deleted from memory_store: '{memory}'"
        return f"Memory not found: '{memory}'"

@tool
def clear_memories(user_id: Optional[str] = None) -> str:
    """
    Remove all stored memories for a user, effectively resetting their conversation history.

    USE THIS TOOL WHEN:
    - The user explicitly asks to forget all information about them
    - The user requests a fresh start or reset
    - The user expresses privacy concerns about stored information
    - You need to clear all outdated or incorrect information at once

    DO NOT USE THIS TOOL WHEN:
    - The user only wants to delete specific memories (use delete_memory instead)
    - The user is just providing new information (use add_memory instead)
    - The user hasn't explicitly asked to forget everything
    - The information might still be useful in future conversations

    EXAMPLE QUERIES:
    - "Please forget everything about me"
    - "Clear all my data"
    - "I want a fresh start, delete all my information"
    - "Reset our conversation history"

    Args:
        user_id (str, optional): The user ID to clear all memories for. If not provided,
                                uses the current user ID from the request context.

    Returns:
        str: Confirmation message indicating whether all memories were successfully cleared
             or that no memories were found
    """
    # Get user_id from context if not provided
    if not user_id:
        try:
            user_id = g.seabex_api.user_id
        except (AttributeError, KeyError):
            return "Error: No user ID available. Please provide a user_id parameter."

    # Create memory key for this user
    memory_key = f"memory:{user_id}"

    # Delete the memory key from both Redis and in-memory store
    redis_success = False
    memory_store_success = False

    # Try Redis first
    try:
        result = redis_client.delete(memory_key)
        if result:
            redis_success = True
    except redis.exceptions.ReadOnlyError:
        print(f"Redis is in read-only mode, can't clear memories for user {user_id}")
    except Exception as e:
        print(f"Error clearing memories from Redis: {str(e)}")

    # Try in-memory store
    try:
        memory_store_success = memory_store.delete_memory(user_id)
    except Exception as e:
        print(f"Error clearing memories from memory_store: {str(e)}")

    # Return appropriate message
    if redis_success and memory_store_success:
        return "All memories cleared successfully from Redis and memory_store."
    elif redis_success:
        return "All memories cleared successfully from Redis."
    elif memory_store_success:
        return "All memories cleared successfully from memory_store."
    else:
        return "No memories found for this user."
