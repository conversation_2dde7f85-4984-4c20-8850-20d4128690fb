import os
import random
import string
import time
import traceback
import requests
import json
from dotenv import load_dotenv
import colorama
from colorama import Fore, Style

# Initialize colorama
colorama.init()

# Load environment variables
load_dotenv()

token = os.getenv('AUTH_TOKEN')
base_url = "http://127.0.0.1:8080"

def send_prompt(prompt, session_id, user_id, chat_history="", token=None):
    """Send a prompt to the Magonia API and return the response."""
    url = f"{base_url}/magonia/chat"
    
    data = {
        "session_id": session_id,
        "prompt": prompt,
        "chat_history": chat_history,
        "scopes": ["magonia-api"],
        "user_id": user_id
    }
    
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {token}" if token else ""
    }
    
    try:
        response = requests.post(url, headers=headers, data=json.dumps(data))
        
        if response.status_code != 200:
            print(f"{Fore.RED}Error: {response.status_code}{Style.RESET_ALL}")
            print(f"{Fore.RED}{response.text}{Style.RESET_ALL}")
            return None
        
        return response.json()
    except Exception as e:
        print(f"{Fore.RED}Error: {str(e)}{Style.RESET_ALL}")
        traceback.print_exc()
        return None

def append_to_chat_history(chat_history, prompt, response):
    """Append a new human-AI interaction to the chat history."""
    return f"{chat_history}Human: {prompt}\nAI: {response}\n"

def print_welcome_message():
    """Print a welcome message for the interactive chat."""
    print(f"{Fore.CYAN}=" * 80)
    print(f"{Fore.CYAN}Welcome to the Magonia Interactive Chat!{Style.RESET_ALL}")
    print(f"{Fore.CYAN}Type your messages and chat with the AI assistant.{Style.RESET_ALL}")
    print(f"{Fore.CYAN}Type 'exit', 'quit', or 'bye' to end the conversation.{Style.RESET_ALL}")
    print(f"{Fore.CYAN}=" * 80)
    print()

def main():
    """Main function to run the interactive chat."""
    print_welcome_message()
    
    # Generate a random session ID and user ID
    session_id = ''.join(random.choice(string.ascii_letters + string.digits) for _ in range(10))
    user_id = ''.join(random.choice(string.ascii_letters + string.digits) for _ in range(10))
    
    print(f"{Fore.YELLOW}Session ID: {session_id}{Style.RESET_ALL}")
    print(f"{Fore.YELLOW}User ID: {user_id}{Style.RESET_ALL}")
    print()
    
    # Initialize chat history
    chat_history = ""
    
    # Start the conversation with a greeting
    print(f"{Fore.GREEN}AI: Hello! I'm your Magonia assistant. How can I help you today?{Style.RESET_ALL}")
    
    while True:
        # Get user input
        user_input = input(f"{Fore.BLUE}You: {Style.RESET_ALL}")
        
        # Check if the user wants to exit
        if user_input.lower() in ['exit', 'quit', 'bye']:
            print(f"{Fore.GREEN}AI: Goodbye! Have a great day!{Style.RESET_ALL}")
            break
        
        # Send the prompt to the API
        response = send_prompt(user_input, session_id, user_id, chat_history, token)
        
        if response:
            # Print the AI's response
            print(f"{Fore.GREEN}AI: {response}{Style.RESET_ALL}")
            
            # Update the chat history
            chat_history = append_to_chat_history(chat_history, user_input, response)
        else:
            print(f"{Fore.RED}Failed to get a response from the AI.{Style.RESET_ALL}")

if __name__ == "__main__":
    main()
