from typing import Optional
from flask import g
from langchain_core.tools import tool

@tool
def get_all_user_areas_with_children(tool_input: Optional[str] = None) -> dict:
    """
    Retrieve a complete hierarchical list of all the user's fields, areas, and their sub-areas.

    USE THIS TOOL WHEN:
    - The user asks for a list of all their fields or areas
    - The user wants to know what fields they have
    - The user needs an overview of their farm structure
    - The user asks about the organization of their agricultural areas

    DO NOT USE THIS TOOL WHEN:
    - The user asks about a specific field's details (use field-specific tools)
    - The user asks about irrigation or soil moisture (use specialized tools)
    - The user asks about historical data for fields
    - The user is asking about someone else's fields

    EXAMPLE QUERIES:
    - "What fields do I have?"
    - "List all my agricultural areas"
    - "Show me all my fields and their organization"
    - "What are all the areas I'm managing?"

    Args:
        tool_input (str, optional): Ignored parameter, exists for compatibility.

    Returns:
        dict: A hierarchical summary of all user areas with their child areas,
              including names and identifiers, or an error message if data retrieval fails.
    """
    try:
        response = g.seabex_api.tools().areas().call_tool(
            'get_all_user_areas_with_children'
        )

        if isinstance(response, dict) and 'error' in response:
            error_message = response['error']
            return {"message": f"Error: {error_message}"}

        return response  # Directly return the response

    except Exception as e:
        print(f"Error retrieving user areas: {e}")
        return {
            "message": "I'm sorry, I couldn't retrieve the user areas at the moment. Please try again later."
        }
