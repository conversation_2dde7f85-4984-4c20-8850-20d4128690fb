from datetime import datetime, timed<PERSON>ta
from typing import Optional
from langchain_core.tools import tool

@tool
def get_current_time_date(question: Optional[str] = None) -> str:
    """
    Retrieve the current date and time in YYYY-MM-DD HH:MM:SS format.

    USE THIS TOOL WHEN:
    - The user asks for the current date or time
    - The user asks what day it is today
    - The user needs to know the current date for planning irrigation
    - The user asks about today's date in any format

    DO NOT USE THIS TOOL WHEN:
    - The user asks about a specific future or past date
    - The user asks about tomorrow, next week, or next month (use the specific tools for those)
    - The user is asking about irrigation data for today (use irrigation-specific tools instead)

    EXAMPLE QUERIES:
    - "What's the date today?"
    - "What time is it?"
    - "What's today's date?"
    - "Can you tell me the current date?"

    Args:
        question (str, optional): The user's question. This is optional and used for context,
                                 but the tool will always return the current date and time.

    Returns:
        str: A response containing the current date and time in YYYY-MM-DD HH:MM:SS format.
    """
    try:
        # Get the current date and time
        now = datetime.now()

        # Format the date and time
        current_time_date = now.strftime('%Y-%m-%d %H:%M:%S')

        # Craft the final response
        if question:
            return f"The current date and time is {current_time_date}. You asked: '{question}'."
        else:
            return f"The current date and time is {current_time_date}."

    except Exception as e:
        print(f"Error retrieving current date and time: {e}")
        return "I'm sorry, I couldn't retrieve the current date and time at the moment."

@tool
def get_tomorrow(dummy: Optional[str] = None) -> str:
    """
    Retrieve tomorrow's date in 'YYYY-MM-DD' format.

    USE THIS TOOL WHEN:
    - The user specifically asks about tomorrow's date
    - The user needs to plan irrigation for tomorrow
    - The user asks what day it will be tomorrow

    DO NOT USE THIS TOOL WHEN:
    - The user asks about today's date (use get_current_time_date instead)
    - The user asks about dates further in the future (use get_next_week or get_next_month)
    - The user is asking about irrigation data for a specific date (use irrigation-specific tools)

    EXAMPLE QUERIES:
    - "What's the date tomorrow?"
    - "What will tomorrow's date be?"
    - "I need to know tomorrow's date for planning"

    Args:
        dummy (str, optional): Ignored parameter, exists for compatibility.

    Returns:
        str: Tomorrow's date formatted as 'YYYY-MM-DD'.
    """
    tomorrow = (datetime.now() + timedelta(days=1)).strftime('%Y-%m-%d')
    return tomorrow

@tool
def get_next_week(dummy: Optional[str] = None) -> str:
    """
    Retrieve the date for the same day next week in 'YYYY-MM-DD' format.

    USE THIS TOOL WHEN:
    - The user specifically asks about the date next week
    - The user needs to plan irrigation for next week
    - The user asks what day it will be in 7 days

    DO NOT USE THIS TOOL WHEN:
    - The user asks about today's date (use get_current_time_date instead)
    - The user asks about tomorrow's date (use get_tomorrow instead)
    - The user asks about dates further in the future (use get_next_month)
    - The user is asking about irrigation data for a specific date (use irrigation-specific tools)

    EXAMPLE QUERIES:
    - "What's the date next week?"
    - "What will the date be in 7 days?"
    - "I need to know next week's date for planning"

    Args:
        dummy (str, optional): Ignored parameter, exists for compatibility.

    Returns:
        str: The date for next week in 'YYYY-MM-DD' format.
    """
    next_week = (datetime.now() + timedelta(weeks=1)).strftime('%Y-%m-%d')
    return next_week

@tool
def get_next_month(dummy: Optional[str] = None) -> str:
    """
    Retrieve the date for the same day next month in 'YYYY-MM-DD' format.

    USE THIS TOOL WHEN:
    - The user specifically asks about the date next month
    - The user needs to plan irrigation for next month
    - The user asks what day it will be in about 30 days

    DO NOT USE THIS TOOL WHEN:
    - The user asks about today's date (use get_current_time_date instead)
    - The user asks about tomorrow's date (use get_tomorrow instead)
    - The user asks about next week's date (use get_next_week instead)
    - The user is asking about irrigation data for a specific date (use irrigation-specific tools)

    EXAMPLE QUERIES:
    - "What's the date next month?"
    - "What will the date be in a month from now?"
    - "I need to know next month's date for planning"

    Args:
        dummy (str, optional): Ignored parameter, exists for compatibility.

    Returns:
        str: The date for next month in 'YYYY-MM-DD' format.
    """
    now = datetime.now()
    next_month = (now.replace(day=1) + timedelta(days=31)).replace(day=1).strftime('%Y-%m-%d')
    return next_month