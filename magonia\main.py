import os
from dotenv import load_dotenv

load_dotenv()
from decorators.token_required import token_required
from flask import Flask, g, jsonify, request
from flask_cors import CORS

from configs.Config import Config
from blueprints.chat import chat_blueprint
from blueprints.health import health_blueprint
from blueprints.memory import memory_blueprint
from magonia.seabex_api import SeabexAPI
from magonia.redis_client import redis_client

def create_application():
    app = Flask(__name__)

    app.config.from_object(Config)

    CORS(app, resources={
        r"/*": {
            "origins": Config.ALLOWED_ORIGINS,
            "allow_headers": ["Content-Type", "Authorization"],
            "methods": ["*"],
            "supports_credentials": True
        }
    })

    app.register_blueprint(chat_blueprint)
    app.register_blueprint(health_blueprint)
    app.register_blueprint(memory_blueprint)

    return app

app = create_application()

@app.before_request
def before_request_func():
    """Function to run before each request."""
    data = request.get_json() or {}
    scopes = data.get('scopes')
    user_id = data.get('user_id')

    if not scopes or not isinstance(scopes, list):
        return jsonify({'error': 'Scopes are required and must be an array.'}), 400
    if not user_id:
        return jsonify({'error': 'User ID is required.'}), 400

    g.seabex_api = SeabexAPI(os.getenv("SEABEX_CLIENT_ID"), os.getenv("SEABEX_CLIENT_SECRET"))
    g.seabex_api.set_scopes(scopes)
    g.seabex_api.set_user_id(user_id)
    g.seabex_api.authenticate()

@app.route('/history', methods=['GET'])
@token_required
def get_history():
    """Endpoint to retrieve chat history for a session."""
    session_id = request.args.get('session_id')

    if not session_id:
        return jsonify({'error': 'session_id is required.'}), 400

    chat_history = redis_client.get(session_id)
    if chat_history is None:
        return jsonify({'error': 'No chat history found for this session.'}), 404

    return jsonify({'chat_history': chat_history.decode('utf-8')})

@app.route('/flush', methods=['DELETE'])
@token_required
def flush_history():
    """Endpoint to flush chat history for a session."""
    session_id = request.args.get('session_id')

    if not session_id:
        return jsonify({'error': 'session_id is required.'}), 400

    result = redis_client.delete(session_id)
    if result == 0:
        return jsonify({'error': 'No chat history found for this session.'}), 404

    return jsonify({'message': 'Chat history flushed successfully.'})

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint."""
    return jsonify({'status': 'healthy'}), 200

if __name__ == '__main__':
    app.run(
        debug=app.config['DEBUG'],
        host="0.0.0.0",
        port=int(app.config['PORT'])
    )
