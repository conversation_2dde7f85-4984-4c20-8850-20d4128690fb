[tool.poetry]
name = "magonia"
version = "0.1.0"
description = ""
authors = ["<PERSON><PERSON><PERSON> <<EMAIL>>"]
readme = "README.md"

[tool.poetry.dependencies]
python = "^3.10"
flask = "^3.0.3"
flask-cors = "^5.0.0"
redis = "^5.1.1"
langchain = "^0.3.2"
langchain-openai = "^0.2.2"
langchain-redis = "^0.1.0"
python-dotenv = "^1.0.1"

[tool.poetry.plugins.dotenv]
location = ".env"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
