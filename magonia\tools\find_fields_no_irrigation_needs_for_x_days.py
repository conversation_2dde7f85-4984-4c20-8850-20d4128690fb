from typing import Optional
from flask import g
from langchain_core.tools import tool

from utils.utils import parse_integer_from_string

@tool
def find_fields_no_irrigation_needs_for_x_days(days: Optional[int] = 7) -> dict:
    """
    Identify fields that are predicted to have no irrigation requirements for the next X days.

    USE THIS TOOL WHEN:
    - The user asks which fields don't need irrigation
    - The user wants to know which fields can be skipped in irrigation planning
    - The user asks about fields with sufficient soil moisture
    - The user wants to identify fields that won't need water for a period

    DO NOT USE THIS TOOL WHEN:
    - The user asks which fields DO need irrigation (use irrigation requirement tools)
    - The user asks about a specific field (use field-specific tools)
    - The user asks about soil moisture levels rather than irrigation needs
    - The user asks about irrigation for a specific date (use date-specific tools)

    EXAMPLE QUERIES:
    - "Which fields won't need irrigation in the next week?"
    - "Are there any fields I can skip irrigating for the next few days?"
    - "Which of my fields have sufficient water for the coming days?"
    - "Tell me which fields don't need water for the next 10 days"

    Args:
        days (int, optional): The number of future days to check for no irrigation needs.
                             Default is 7. Must be a positive integer.

    Returns:
        dict: A list of fields predicted to have no irrigation needs in the specified period,
              including field names and relevant details, or an error message if data retrieval fails.
    """
    try:
        days =  parse_integer_from_string(days)

        if not isinstance(days, int):
            return {"message": "The 'days' parameter must be an integer."}

        response = g.seabex_api.tools().irrigations().call_tool(
            'find_fields_no_irrigation_needs_for_x_days',
            {'days': days}
        )

        if isinstance(response, dict) and 'error' in response:
            error_message = response['error']
            return {"message": f"Error: {error_message}"}


        return response

    except Exception as e:
        print(f"Error finding fields with no irrigation needs: {e}")
        return {
            "message": "I'm sorry, I couldn't find the irrigation needs at the moment. Please try again later."
        }
