from flask import Blueprint, jsonify, request
from decorators.token_required import token_required
from magonia.redis_client import redis_client
import json
import redis

memory_blueprint = Blueprint('memory', __name__)

@memory_blueprint.route('/memory', methods=['POST'])
@token_required
def add_memory_route():
    """Add a new memory for a user."""
    data = request.json

    if 'memory' not in data or 'user_id' not in data:
        return jsonify({"error": "Missing required fields."}), 400

    memory = data.get('memory')
    user_id = data.get('user_id')

    # Create memory key for this user
    memory_key = f"memory:{user_id}"

    # Get existing memories or create new list
    existing_memories = redis_client.get(memory_key)
    if existing_memories:
        memories = json.loads(existing_memories.decode('utf-8'))
    else:
        memories = []

    # Add new memory if it doesn't already exist
    if memory not in memories:
        memories.append(memory)
        try:
            redis_client.set(memory_key, json.dumps(memories))
            return jsonify({"message": f"Memory stored successfully: '{memory}'"}), 200
        except redis.exceptions.ReadOnlyError:
            return jsonify({"message": f"Memory not stored: Redis is in read-only mode", "memory": memory}), 503
        except Exception as e:
            return jsonify({"message": f"Memory not stored: {str(e)}", "memory": memory}), 500
    else:
        return jsonify({"message": f"This memory already exists: '{memory}'"}), 200

@memory_blueprint.route('/memory', methods=['GET'])
#@token_required
def get_memories_route():
    """Get all memories for a user."""
    user_id = request.args.get('user_id')

    if not user_id:
        return jsonify({"error": "Missing user_id parameter."}), 400

    # Create memory key for this user
    memory_key = f"memory:{user_id}"

    # Get existing memories
    existing_memories = redis_client.get(memory_key)
    if existing_memories:
        memories = json.loads(existing_memories.decode('utf-8'))
        return jsonify({"memories": memories}), 200
    else:
        return jsonify({"memories": []}), 200

@memory_blueprint.route('/memory', methods=['DELETE'])
#@token_required
def delete_memory_route():
    """Delete a specific memory or all memories for a user."""
    user_id = request.args.get('user_id')
    memory = request.args.get('memory')

    if not user_id:
        return jsonify({"error": "Missing user_id parameter."}), 400

    # Create memory key for this user
    memory_key = f"memory:{user_id}"

    # If memory is provided, delete only that memory
    if memory:
        existing_memories = redis_client.get(memory_key)
        if not existing_memories:
            return jsonify({"message": "No memories found for this user."}), 404

        memories = json.loads(existing_memories.decode('utf-8'))

        if memory in memories:
            memories.remove(memory)
            try:
                redis_client.set(memory_key, json.dumps(memories))
                return jsonify({"message": f"Memory deleted successfully: '{memory}'"}), 200
            except redis.exceptions.ReadOnlyError:
                return jsonify({"message": f"Memory not deleted: Redis is in read-only mode", "memory": memory}), 503
            except Exception as e:
                return jsonify({"message": f"Memory not deleted: {str(e)}", "memory": memory}), 500
        else:
            return jsonify({"message": f"Memory not found: '{memory}'"}), 404

    # If no memory is provided, delete all memories
    else:
        try:
            result = redis_client.delete(memory_key)
            if result:
                return jsonify({"message": "All memories cleared successfully."}), 200
            else:
                return jsonify({"message": "No memories found for this user."}), 404
        except redis.exceptions.ReadOnlyError:
            return jsonify({"message": "Memories not cleared: Redis is in read-only mode"}), 503
        except Exception as e:
            return jsonify({"message": f"Memories not cleared: {str(e)}"}), 500
